package main

import (
	"context"
	"time"

	"ps-socket-server/pkg/initialization"
	"ps-socket-server/pkg/response"
	_ "ps-socket-server/pkg/routers"

	beego "github.com/beego/beego/v2/server/web"
)

func init() {
	time.Local = time.UTC

	beego.BConfig.RecoverPanic = false
	beego.BConfig.RecoverFunc = response.CustomPanicHandler
	ctx := context.Background()

	err := initialization.Init(ctx)

	if err != nil {
		panic(err)
	}

}

func main() {
	// The HTTPPort will be loaded from conf/app.conf by default
	beego.Run()
}
