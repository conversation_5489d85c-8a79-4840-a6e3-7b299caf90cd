package models 

import (
	dba "ps-socket-server/pkg/db"
	helper "ps-socket-server/pkg/helpers"
)

func GetUserLite(Id int64) (UserLite, error) {
	var (
		model UserLite
	)

	query := `
	SELECT 
		id, 
		uid,
		company_id,
		participant_id,
		role_id,
		level_id,
		first_name,
		last_name,
		username,
		COALESCE(access_token,'') as access_token 
	FROM
		users 
	WHERE 
		id = ?`

	err := dba.RawQueryRow(&query, &model, helper.ToPtr("invalid_user_id"), Id)
	return model, err
}