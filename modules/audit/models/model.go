package models

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
)

type SysAudits struct {
	Id            int64     `orm:"pk;auto"`
	ParentId      *int64    `orm:"null"`
	CompanyId     *int64    `orm:"null"`
	UserId        *int64    `orm:"null"`
	ProxyUserId   *int64    `orm:"null"`
	Url           *string   `orm:"null"`
	IpAddress     *string   `orm:"null"`
	UserAgent     *string   `orm:"null"`
	Payload       *string   `orm:"null;type(jsonb)"`
	Response      *string   `orm:"null;type(jsonb)"`
	EntityType    *string   `orm:"null"`
	EntityId      *int64    `orm:"null"`
	EntityAction  *string   `orm:"null"`
	OldJson       *string   `orm:"null;type(jsonb)"`
	NewJson       *string   `orm:"null;type(jsonb)"`
	Created       time.Time `orm:"auto_now_add;type(datetime)"`
	ModuleId      int
	ActivityLogId int
}

type ApiAudits struct {
	UserActivityId int64
	EntityId       int64
	SysAudit       []SysAudits
}

func (u *SysAudits) TableName() string {
	return "sys_audits"
}

func init() {
	orm.RegisterModel(new(SysAudits))
}
