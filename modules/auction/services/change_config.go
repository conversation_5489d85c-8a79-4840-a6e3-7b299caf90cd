package services

import (
	"log"
	"ps-socket-server/modules/auction/models"
	wsService "ps-socket-server/modules/websocket/services"
	consts "ps-socket-server/pkg/consts"
	helper "ps-socket-server/pkg/helpers"
	registry "ps-socket-server/pkg/registry"
	wsResponse "ps-socket-server/pkg/response"
)

func ChangeConfig(projectId *string, lotId *string) error {
	var (
		err    error
		alerts wsResponse.Alerts
		room   registry.Room
	)

	actionIn := string(consts.CHANGE_CONFIG)
	actionOut := string(registry.ActionMap[consts.CHANGE_CONFIG])
	alerts.SetAlert(string(consts.SUCCESS), consts.Toster)

	if lotId != nil {
		room, err = registry.ExtractRoom(nil, lotId)
		if err != nil {
			return err
		}
		wsService.Broadcasting(&room, nil, &actionIn, &actionOut, alerts.ArrAlert, nil, helper.ToPtr(1))
	} else {
		// Change Config for Project
		lotUids, err := models.GetLotUids(projectId)

		log.Println("lotUids", lotUids)

		if err != nil {
			return err
		}

		for _, lotUid := range lotUids {
			room, err = registry.ExtractRoom(nil, &lotUid)
			if err == nil {
				wsService.Broadcasting(&room, nil, &actionIn, &actionOut, alerts.ArrAlert, nil, helper.ToPtr(1))
			}
		}
	}

	return nil
}
