package models

import (
	dba "ps-socket-server/pkg/db"
	helper "ps-socket-server/pkg/helpers"
)

func GetLotUids(projectUid *string) ([]string, error) {

	var lotUids []string

	query := `
	SELECT 
		auc_lot.uid as uid 
	FROM
		auc_lot 
	JOIN 
		project ON project.id = auc_lot.project_id	
	WHERE 
		project.uid = ?`

	err := dba.RawQueryRows(&query, &lotUids, helper.ToPtr("ws_invalid_project_reference"), projectUid)

	if err != nil {
		return nil, err
	}

	return lotUids, nil

}
