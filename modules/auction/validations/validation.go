package validations

import (
	dto "ps-socket-server/modules/auction/dto"
	authValidations "ps-socket-server/modules/auth/validations"
	apiResponse "ps-socket-server/pkg/response"
)

func ValidateChangeConfigReq(e *apiResponse.Errors, changeConfigReq *dto.ChangeConfigReq) {
	authValidations.CheckProjectId(e, changeConfigReq.ProjectId, "project_id", true)
	authValidations.CheckLotId(e, changeConfigReq.LotId, "lot_id", false)
}
