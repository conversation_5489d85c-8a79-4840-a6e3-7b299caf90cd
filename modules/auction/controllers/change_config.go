package controller

import (
	"encoding/json"
	"net/http"

	consts "ps-socket-server/pkg/consts"
	apiResponse "ps-socket-server/pkg/response"

	dto "ps-socket-server/modules/auction/dto"
	services "ps-socket-server/modules/auction/services"
	auctionValidations "ps-socket-server/modules/auction/validations"
)

func (c *AuctionController) ChangeConfig() {

	var (
		req    dto.ChangeConfigReq
		errors apiResponse.Errors
		alerts apiResponse.Alerts
	)

	// Create Audit Log
	c.ShouldLog = true

	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		alerts.SetAlert(string(consts.INVALID_JSON), consts.Toster)
		c.ApiResponse(http.StatusBadRequest, nil, alerts.ArrAlert, nil)
		return
	}

	auctionValidations.ValidateChangeConfigReq(&errors, &req)
	if errors.ArrError != nil {
		c.ApiResponse(http.StatusBadRequest, errors.ArrError, nil, nil)
		return
	}

	err := services.ChangeConfig(req.ProjectId, req.LotId)

	if err != nil {
		alerts.SetAlert(err.Error(), consts.Toster)
		c.ApiResponse(http.StatusBadRequest, nil, alerts.ArrAlert, nil)
		return
	}

	// Prepare Response
	alerts.SetAlert(string(consts.SUCCESS), consts.Toster)
	c.ApiResponse(http.StatusOK, nil, alerts.ArrAlert, nil)
}
