package models

import (
	dba "ps-socket-server/pkg/db"
	helper "ps-socket-server/pkg/helpers"
	registry "ps-socket-server/pkg/registry"
)

func CompleteRoomDefinition(room *registry.Room) {

	query := `
	SELECT 
		company.uid as company_id,
		company.name as company_name,
		auc_lot.lot_title  as lot_title 
	FROM
		auc_lot 
	JOIN 
		project ON project.id = auc_lot.project_id	
	JOIN 
		company ON company.id = project.company_id 
	WHERE 
		auc_lot.uid = ?`

	err := dba.RawQueryRow(&query, room, helper.ToPtr("ws_invalid_reference"), &room.LotId)

	if err != nil {
		panic(err)
	}
}
