package services

import (
	"net/http"
	validations "ps-socket-server/modules/websocket/validations"
	consts "ps-socket-server/pkg/consts"
	registry "ps-socket-server/pkg/registry"
	wsResponse "ps-socket-server/pkg/response"
)

func (s *WebSocketService) Broadcast() {
	var (
		alerts wsResponse.Alerts
		room   registry.Room
	)

	validations.ValidateBroadcastRequest(&alerts, s.wsReq)
	if alerts.ArrAlert != nil {
		WsResponse(http.StatusBadRequest, alerts.ArrAlert, nil, nil, s.conn, s.messageType)
		return
	}

	room, err := registry.ExtractRoom(&s.wsReq.Auth<PERSON>ey, nil)
	if err != nil {
		alerts.SetAlert(err.Error(), consts.Toster)
		WsResponse(http.StatusBadRequest, alerts.ArrAlert, nil, nil, s.conn, s.messageType)
		return
	}

	actionOut := string(registry.ActionMap[consts.ActionIn(s.wsReq.Action)])
	alerts.SetAlert(string(consts.SUCCESS), consts.Toster)

	Broadcasting(&room, s.conn, &s.wsReq.Action, &actionOut, alerts.ArrAlert, &s.wsReq.Data, &s.messageType)

	/*
		for _, conn := range room.Connections {
			if conn.Conn != s.conn {
				log.Println(actionOut, " to ", conn.User.Username)
				WsResponse(http.StatusOK, alerts.ArrAlert, &actionOut, s.wsReq.Data, conn.Conn, s.messageType)
			} else {
				log.Println(s.wsReq.Action, " from ", conn.User.Username)
			}
		}
	*/
}
