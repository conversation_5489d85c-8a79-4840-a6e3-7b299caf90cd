package services

import (
	"net/http"
	consts "ps-socket-server/pkg/consts"
	registry "ps-socket-server/pkg/registry"
	wsResponse "ps-socket-server/pkg/response"
)

func (s *WebSocketService) NewBid() {
	var (
		alerts wsResponse.Alerts
		room   registry.Room
	)

	room, err := registry.ExtractRoom(&s.wsReq.AuthKey, nil)
	if err != nil {
		alerts.SetAlert(err.Error(), consts.Toster)
		WsResponse(http.StatusBadRequest, alerts.ArrAlert, nil, nil, s.conn, s.messageType)
		return
	}

	actionOut := string(registry.ActionMap[consts.ActionIn(s.wsReq.Action)])
	alerts.SetAlert(string(consts.SUCCESS), consts.Toster)

	Broadcasting(&room, s.conn, &s.wsReq.Action, &actionOut, alerts.ArrAlert, &s.wsReq.Data, &s.messageType)

	/*
		for _, conn := range room.Connections {
			if conn.Conn != s.conn {
				log.Println(actionOut, " to ", conn.User.Username)
				WsResponse(http.StatusOK, alerts.ArrAlert, &actionOut, s.wsReq.Data, conn.Conn, s.messageType)
			} else {
				log.Println(s.wsReq.Action, " from ", conn.User.Username)
			}
		}
	*/
}

func (s *WebSocketService) InvalidateBid() {
	var (
		alerts wsResponse.Alerts
		room   registry.Room
	)

	room, err := registry.ExtractRoom(&s.wsReq.AuthKey, nil)
	if err != nil {
		alerts.SetAlert(err.Error(), consts.Toster)
		WsResponse(http.StatusBadRequest, alerts.ArrAlert, nil, nil, s.conn, s.messageType)
		return
	}

	actionOut := string(registry.ActionMap[consts.ActionIn(s.wsReq.Action)])
	alerts.SetAlert(string(consts.SUCCESS), consts.Toster)

	Broadcasting(&room, s.conn, &s.wsReq.Action, &actionOut, alerts.ArrAlert, &s.wsReq.Data, &s.messageType)

	/*
		for _, conn := range room.Connections {
			if conn.Conn != s.conn {
				log.Println(actionOut, " to ", conn.User.Username)
				WsResponse(http.StatusOK, alerts.ArrAlert, &actionOut, s.wsReq.Data, conn.Conn, s.messageType)
			} else {
				log.Println(s.wsReq.Action, " from ", conn.User.Username)
			}
		}
	*/
}
