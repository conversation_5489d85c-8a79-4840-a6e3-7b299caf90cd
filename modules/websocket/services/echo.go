package services

import (
	"net/http"
	consts "ps-socket-server/pkg/consts"
	registry "ps-socket-server/pkg/registry"
	wsResponse "ps-socket-server/pkg/response"
)

func (s *WebSocketService) Echo() {
	var (
		alerts wsResponse.Alerts
	)

	actionOut := string(registry.ActionMap[consts.ActionIn(s.wsReq.Action)])

	alerts.SetAlert(string(consts.SUCCESS), consts.Toster)
	WsResponse(http.StatusOK, alerts.ArrAlert, &actionOut, s.wsReq.Data, s.conn, s.messageType)
}
