package services

import (
	"fmt"
	"net/http"
	"time"

	models "ps-socket-server/modules/websocket/models"
	registry "ps-socket-server/pkg/registry"

	"github.com/gorilla/websocket"
)

// Upgrader for WebSocket connections
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// We will validate auth key instead of origin
		return true
	},
}

// CreateSocketConnection handles the WebSocket connection upgrade and validation
func CreateSocketConnection(w http.ResponseWriter, r *http.Request, authKey string) (*websocket.Conn, error) {

	var (
		verifiedUser registry.User
		conn         *websocket.Conn
		exists       bool
		err          error
		room         registry.Room
		roomKey      string
		wsConn       registry.WebSocketConnection
	)

	verifiedUser, exists = registry.VerifiedUserRegistry[authKey]
	if !exists {
		return nil, fmt.Errorf("unauthorized: invalid auth key")
	}

	// Upgrade HTTP connection to WebSocket
	conn, err = upgrader.Upgrade(w, r, nil)
	if err != nil {
		return nil, err
	}

	// Create WebSocketConnection instance
	wsConn = registry.WebSocketConnection{
		Conn:    conn,
		User:    verifiedUser,
		Created: time.Now().Unix(),
	}

	// Create room key (ProjectId###LotId)
	roomKey = verifiedUser.LotId

	// Check if room exists in RoomRegistry
	room, exists = registry.RoomRegistry[roomKey]
	if !exists {
		// Create new room
		room = registry.Room{
			ProjectId:   verifiedUser.ProjectId,
			LotId:       verifiedUser.LotId,
			Connections: make(map[string]registry.WebSocketConnection),
			Created:     time.Now().Unix(),
		}

		// Complete a room definition using model function
		models.CompleteRoomDefinition(&room)

	}

	// Add connection to room
	room.Connections[authKey] = wsConn

	// Update room in registry
	registry.RoomRegistry[roomKey] = room

	// Remove verified user from registry
	delete(registry.VerifiedUserRegistry, authKey)

	return conn, nil
}
