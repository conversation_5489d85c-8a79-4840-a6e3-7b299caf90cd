package services

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	dto "ps-socket-server/modules/websocket/dto"
	consts "ps-socket-server/pkg/consts"
	logger "ps-socket-server/pkg/logger"
	registry "ps-socket-server/pkg/registry"
	wsResponse "ps-socket-server/pkg/response"

	websocket "github.com/gorilla/websocket"
)

// WebSocketService operations for WebSocket
type WebSocketService struct {
	conn        *websocket.Conn
	messageType int
	wsReq       *dto.WsReq
}

func NewWebSocketService(conn *websocket.Conn, messageType int, wsReq *dto.WsReq) *WebSocketService {
	return &WebSocketService{
		conn:        conn,
		messageType: messageType,
		wsReq:       wsReq,
	}
}

func (s *WebSocketService) ProcessWsRequests() {

	switch consts.ActionIn(s.wsReq.Action) {
	case consts.ECHO:
		s.Echo()
	case consts.CLOSED:
		s.Closed()
	case consts.BROADCAST:
		s.Broadcast()
	case consts.NEW_BID:
		s.NewBid()
	case consts.INVALIDATE_BID:
		s.InvalidateBid()
	}
}

func Broadcasting(room *registry.Room, connExclude *websocket.Conn, actionIn *string, actionOut *string, ArrAlert []*wsResponse.Alert, data *map[string]interface{}, messageType *int) {
	for _, conn := range room.Connections {
		if conn.Conn != connExclude {
			log.Println(*actionOut, " to ", conn.User.Username)
			WsResponse(http.StatusOK, ArrAlert, actionOut, data, conn.Conn, *messageType)
		} else {
			log.Println(*actionIn, " from ", conn.User.Username)
		}
	}
}

func WsResponse(httpStatusCode int, alerts []*wsResponse.Alert, action *string, data interface{}, conn *websocket.Conn, messageType int) {

	log.Println("Data :: ", data)

	for _, alert := range alerts {
		if alert.Mesg == string(consts.SOMETHING_WENT_WRONG) {
			httpStatusCode = http.StatusInternalServerError
			break
		}
	}

	response := wsResponse.Response{
		Status: wsResponse.Status{
			Code:   httpStatusCode,
			Alerts: alerts,
		},
	}

	if data != nil {
		// Check if it's a map or a reference to a map
		if dataPtr, ok := data.(*map[string]interface{}); ok {
			// Check if it's a pointer to a map
			if dataPtr != nil && len(*dataPtr) > 0 {
				response.Data = data
			}
		} else if dataMap, ok := data.(map[string]interface{}); ok {
			// Check if the map has elements
			if len(dataMap) > 0 {
				response.Data = data
			}
		} else {
			// It's not a map, keep the original data
			response.Data = data
		}
	} else {
		// Data is nil, keep it nil
		//response.Data = nil
	}

	if action != nil {
		response.Action = *action
	}

	respBytes, _ := json.Marshal(response)
	err := conn.WriteMessage(messageType, respBytes)
	if err != nil {
		logger.LogException(fmt.Errorf("write error: %v", err), consts.Error)
	}
}
