package controller

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"runtime/debug"

	dto "ps-socket-server/modules/websocket/dto"
	services "ps-socket-server/modules/websocket/services"
	validations "ps-socket-server/modules/websocket/validations"
	consts "ps-socket-server/pkg/consts"
	logger "ps-socket-server/pkg/logger"

	wsResponse "ps-socket-server/pkg/response"

	"github.com/gorilla/websocket"
)

// handleWebSocketConnection handles the WebSocket connection lifecycle and message processing
// It reads messages from the client, processes them, and sends responses back
func (c *WebSocketController) handleWebSocketConnection(conn *websocket.Conn) {

	for {
		var (
			wsReq  dto.WsReq
			alerts wsResponse.Alerts
		)

		messageType, message, err := conn.ReadMessage()

		log.Println("messageType :: ", messageType)

		if err != nil {
			e := fmt.Errorf("messageType :: %d, message :: %s, read error:: %v", messageType, string(message), err)
			logger.LogException(e, consts.Error)

			wsReq.Action = string(consts.CLOSED)

			services.NewWebSocketService(conn, messageType, &wsReq).ProcessWsRequests()

			break
		}

		fmt.Printf("Received message of type %d: %s\n", messageType, string(message))

		if messageType == websocket.CloseMessage {
			log.Println("message ", string(message))
		} else {

			if err := json.Unmarshal(message, &wsReq); err != nil {
				e := fmt.Errorf("JSON parse error: %v", err)
				logger.LogException(e, consts.Error)
				alerts.SetAlert(e.Error(), consts.Toster)
				services.WsResponse(http.StatusBadRequest, alerts.ArrAlert, nil, nil, conn, messageType)
				continue
			}

			validations.ValidateWsRequest(&alerts, &wsReq)
			if alerts.ArrAlert != nil {
				services.WsResponse(http.StatusBadRequest, alerts.ArrAlert, nil, nil, conn, messageType)
				continue
			}

			services.NewWebSocketService(conn, messageType, &wsReq).ProcessWsRequests()
		}

	}
}

// WSPanicHandler wraps a WebSocket handler function to provide panic recovery
func (c *WebSocketController) WsPanicHandler(handler func(conn *websocket.Conn)) func(conn *websocket.Conn) {
	return func(conn *websocket.Conn) {
		defer func() {
			if r := recover(); r != nil {
				// Get the stack trace as a string
				stackTrace := string(debug.Stack())

				errMsg := fmt.Errorf("panic :: %s\n%s", r, stackTrace)

				logger.LogPanic(errMsg)

				// Create a default action string for the response
				defaultAction := string(consts.DIED)

				var alerts wsResponse.Alerts
				alerts.SetAlert(string(consts.SOMETHING_WENT_WRONG), consts.Toster)
				services.WsResponse(
					http.StatusInternalServerError,
					alerts.ArrAlert,
					&defaultAction,
					nil,
					conn, websocket.TextMessage)

				/*
					// Attempt to close the connection gracefully
					closeMsg := websocket.FormatCloseMessage(
						websocket.CloseInternalServerErr,
						"Internal server error occurred",
					)

					// Try to send close message to client
					err := conn.WriteControl(
						websocket.CloseMessage,
						closeMsg,
						time.Now().Add(time.Second),
					)
					if err != nil {
						LogException(fmt.Errorf("failed to send ws connection close message: %v", err), consts.Warning)
					}
				*/

				// Close the connection
				conn.Close()
			}
		}()

		handler(conn)
	}
}
