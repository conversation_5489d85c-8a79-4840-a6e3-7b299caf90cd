package controller

import (
	"net/http"
	"net/url"
	"strings"

	base "ps-socket-server/modules/base/controllers"
	services "ps-socket-server/modules/websocket/services"
	consts "ps-socket-server/pkg/consts"
	apiResponse "ps-socket-server/pkg/response"
)

// WebSocketController operations for WebSocket
type WebSocketController struct {
	base.BaseController
}

func NewWebSocketController() *WebSocketController {
	return &WebSocketController{}
}

// HandleWebSocket handles WebSocket connection requests
func (c *WebSocketController) HandleWebSocket() {

	var (
		alerts apiResponse.Alerts
	)

	// Get the raw query string
	rawQuery := c.Ctx.Request.URL.RawQuery

	// Extract the auth_key value without standard URL decoding
	authKey := ""
	authKeyPrefix := "auth_key="

	// Find the auth_key parameter in the raw query
	authKeyIndex := strings.Index(rawQuery, authKeyPrefix)
	if authKeyIndex != -1 {
		// Extract everything after "auth_key="
		valueStart := authKeyIndex + len(authKeyPrefix)
		valueEnd := len(rawQuery)

		// Find the end of the value (next & or end of string)
		ampIndex := strings.Index(rawQuery[valueStart:], "&")
		if ampIndex != -1 {
			valueEnd = valueStart + ampIndex
		}

		// Extract the raw value
		authKey = rawQuery[valueStart:valueEnd]

		// Only decode %xx sequences but preserve + characters
		decodedAuthKey, err := url.PathUnescape(authKey)
		if err == nil {
			authKey = decodedAuthKey
		}
	} else {
		alerts.SetAlert("failed to find auth_key in URL query", consts.Toster)
		resp := apiResponse.WsResponse(http.StatusBadRequest, nil, alerts.ArrAlert, nil)
		c.Data["json"] = resp
		c.ServeJSON()
		return
	}

	// Handle WebSocket connection
	conn, err := services.CreateSocketConnection(c.Ctx.ResponseWriter, c.Ctx.Request, authKey)

	if err != nil {
		alerts.SetAlert("Unauthorized", consts.Toster)
		resp := apiResponse.WsResponse(http.StatusUnauthorized, nil, alerts.ArrAlert, nil)
		c.Data["json"] = resp
		c.ServeJSON()
		return
	}

	alerts.SetAlert(string(consts.SUCCESS), consts.Toster)
	resp := apiResponse.WsResponse(http.StatusOK, nil, alerts.ArrAlert, nil)

	conn.WriteJSON(resp)

	// Wrap the WebSocket connection handler with panic recovery
	c.WsPanicHandler(c.handleWebSocketConnection)(conn)
}
