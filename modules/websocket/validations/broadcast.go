package validations

import (
	"strings"

	dto "ps-socket-server/modules/websocket/dto"
	consts "ps-socket-server/pkg/consts"
	wsResponse "ps-socket-server/pkg/response"
)

func ValidateBroadcastRequest(alerts *wsResponse.Alerts, wsReq *dto.WsReq) {
	// extract message key from wsReq.Data
	CheckMessage(alerts, wsReq.Data)
}

func CheckMessage(alerts *wsResponse.Alerts, obj map[string]interface{}) {
	value, ok := obj["message"].(string)

	if !ok || len(strings.TrimSpace(value)) == 0 {
		alerts.SetAlert("WS_MESSAGE_REQUIRED", consts.Toster)
	}
}
