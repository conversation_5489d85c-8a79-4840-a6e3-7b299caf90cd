package validations 

import (
	"strings"
	
	registry "ps-socket-server/pkg/registry"
	dto "ps-socket-server/modules/websocket/dto"
	consts "ps-socket-server/pkg/consts"
	wsResponse "ps-socket-server/pkg/response"
	
)

func ValidateWsRequest(alerts *wsResponse.Alerts, wsReq *dto.WsReq) {
	CheckAuthKey(alerts, wsReq.AuthKey)
	CheckAction(alerts, wsReq.Action)
}

func CheckAuth<PERSON>ey(alerts *wsResponse.Alerts, obj interface{}) {
	value, ok := obj.(string)

	if !ok {
		alerts.SetAlert("WS_AUTH_KEY_MUST_STRING", consts.Toster)
	} else if len(strings.TrimSpace(value)) == 0 {
		alerts.SetAlert("WS_AUTH_KEY_REQUIRED", consts.Toster)
	}
}

func CheckAction(alerts *wsResponse.Alerts, obj interface{}) {
	value, ok := obj.(string)

	if !ok {
		alerts.SetAlert("WS_ACTION_MUST_STRING", consts.Toster)
	} else if len(strings.TrimSpace(value)) == 0 {
		alerts.SetAlert("WS_ACTION_REQUIRED", consts.Toster)
	} else {
		_, exists := registry.ActionMap[consts.ActionIn(value)]; if !exists {
			alerts.SetAlert("WS_INVALID_ACTION", consts.Toster)
		}
	}	
}
