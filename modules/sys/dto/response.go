package dto

type HealthRes struct {
	Status string `json:"status"`
}

type Room struct {
	CompanyId   string `json:"company_id"`
	CompanyName string `json:"company_name"`
	ProjectId   string `json:"project_id"`
	LotId       string `json:"lot_id"`
	LotTitle    string `json:"lot_title"`
	Connections int    `json:"connections"`
	Created     string `json:"created,omitempty"`
}

type RoomsRes struct {
	Total int    `json:"total"`
	Rooms []Room `json:"rooms,omitempty"`
}

type Conn struct {
	UserUId   string `json:"user_uid"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Username  string `json:"username"`
	UserLevel int64  `json:"user_level"`
	Ip        string `json:"ip"`
	Created   string `json:"created,omitempty"`
}

type ConnRes struct {
	Total       int    `json:"total"`
	Connections []Conn `json:"connections,omitempty"`
}
