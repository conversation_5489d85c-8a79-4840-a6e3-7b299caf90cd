package controller

import (
	"net/http"
	base "ps-socket-server/modules/base/controllers"
	dto "ps-socket-server/modules/sys/dto"

	consts "ps-socket-server/pkg/consts"
	apiResponse "ps-socket-server/pkg/response"
)

// SysController operations for Sys
type SysController struct {
	base.BaseController
}

func NewSysController() *SysController {
	return &SysController{}
}

// Get handles GET requests to /health
func (c *SysController) Health() {

	var (
		alerts apiResponse.Alerts
		resp   dto.HealthRes
	)
	// Create Audit Log
	c.ShouldLog = true

	// Set Response
	resp.Status = "Up"

	// Prepare Response
	alerts.SetAlert(string(consts.SUCCESS), consts.Toster)
	c.ApiResponse(http.StatusOK, nil, alerts.ArrAlert, resp)
}
