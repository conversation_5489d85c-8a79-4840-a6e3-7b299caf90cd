package controller

import (
	"encoding/json"
	"net/http"

	consts "ps-socket-server/pkg/consts"
	apiResponse "ps-socket-server/pkg/response"

	dto "ps-socket-server/modules/sys/dto"
	services "ps-socket-server/modules/sys/services"
	sysValidations "ps-socket-server/modules/sys/validations"
)

/*
@Request
========
Case 2: All Rooms
	{
		"ip": "***********"
	}

Case 2: Search for specific Project
	{
		"project_id": "ef7c6c6b-e46c-48a5-9e7c-14e4eafc8caf",
		"ip": "***********"
	}

Case 3: Search for specific Lot
	{
	    "project_id": "ef7c6c6b-e4c6-48a5-9e7c-14e4eafc8caf",
	    "lot_id": "70040337-ed3b-4cb3-ab8f-3ab566e78990",
		"ip": "***********"
	}

@Response
=========
	{
	    "status": {
	        "code": 200,
	        "alerts": [
	            {
	                "display": "toast",
	                "mesg": "SUCCESS"
	            }
	        ]
	    },
	    "data": {
	        "total": 2,
	        "rooms": [
	            {
	                "company_id": "50d2a72e-46be-4d3b-9f48-e346ea980a2e",
	                "company_name": "Demo 1",
	                "project_id": "ef7c6c6b-e4c6-48a5-9e7c-14e4eafc8caf",
	                "lot_id": "70040337-ed3b-4cb3-ab8f-3ab566e78990",
	                "lot_title": " Fasteners M12203, M11700\t\t\t\t\t",
	                "connections": 2,
	                "created": "2025/06/02 05:12:25"
	            },
	            {
	                "company_id": "50d2a72e-46be-4d3b-9f48-e346ea980a2e",
	                "company_name": "Demo 1",
	                "project_id": "d6b4bf69-987e-42cb-bb8a-398197952c2f",
	                "lot_id": "78199129-a56d-43d8-9d90-ab3ece5e78aa",
	                "lot_title": "Vehicles for sale in Sharjah",
	                "connections": 1,
	                "created": "2025/06/02 05:14:43"
	            }
	        ]
	    }
	}
*/

func (c *SysController) RoomsList() {

	var (
		req    dto.RoomsReq
		errors apiResponse.Errors
		alerts apiResponse.Alerts
		resp   dto.RoomsRes
	)

	// Create Audit Log
	c.ShouldLog = true

	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		alerts.SetAlert(string(consts.INVALID_JSON), consts.Toster)
		c.ApiResponse(http.StatusBadRequest, nil, alerts.ArrAlert, nil)
		return
	}

	sysValidations.ValidateRoomsReq(&errors, &req)
	if errors.ArrError != nil {
		c.ApiResponse(http.StatusBadRequest, errors.ArrError, nil, nil)
		return
	}

	resp = services.RoomsList(req.ProjectId, req.LotId)

	// Prepare Response
	alerts.SetAlert(string(consts.SUCCESS), consts.Toster)
	c.ApiResponse(http.StatusOK, nil, alerts.ArrAlert, resp)
}

/*
@Request
=======
{
  "room_key": "70040337-ed3b-4cb3-ab8f-3ab566e78990",
  "ip": "***********"
}

@Respone
========
{
    "status": {
        "code": 200,
        "alerts": [
            {
                "display": "toast",
                "mesg": "SUCCESS"
            }
        ]
    },
    "data": {
        "total": 2,
        "connections": [
            {
                "user_uid": "e9db3c72-b251-4840-bd79-5dfc096d9572",
                "first_name": "John",
                "last_name": "Smith (Owner)",
                "username": "superuser",
                "user_level": 2,
                "ip": "",
                "created": "2025/06/02 09:09:03"
            },
            {
                "user_uid": "35d34693-99f6-43f3-9730-07c98eb7987e",
                "first_name": "Jesse",
                "last_name": " Curry",
                "username": "JESSE_Curry",
                "user_level": 3,
                "ip": "",
                "created": "2025/06/02 09:12:12"
            }
        ]
    }
}
*/
func (c *SysController) ConnList() {

	var (
		req    dto.ConnReq
		errors apiResponse.Errors
		alerts apiResponse.Alerts
		resp   dto.ConnRes
	)

	// Create Audit Log
	c.ShouldLog = true

	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		alerts.SetAlert(string(consts.INVALID_JSON), consts.Toster)
		c.ApiResponse(http.StatusBadRequest, nil, alerts.ArrAlert, nil)
		return
	}

	sysValidations.ValidateConnReq(&errors, &req)
	if errors.ArrError != nil {
		c.ApiResponse(http.StatusBadRequest, errors.ArrError, nil, nil)
		return
	}

	resp, err := services.ConnList(&req.RoomKey)

	if err != nil {
		alerts.SetAlert(err.Error(), consts.Toster)
		c.ApiResponse(http.StatusBadRequest, nil, alerts.ArrAlert, nil)
		return
	}

	// Prepare Response
	alerts.SetAlert(string(consts.SUCCESS), consts.Toster)
	c.ApiResponse(http.StatusOK, nil, alerts.ArrAlert, resp)
}
