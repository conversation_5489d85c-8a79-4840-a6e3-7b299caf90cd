package services

import (
	"errors"
	"time"

	dto "ps-socket-server/modules/sys/dto"
	registry "ps-socket-server/pkg/registry"
)

// RoomsList returns a list of rooms based on the provided projectId and lotId
func RoomsList(projectId *string, lotId *string) dto.RoomsRes {

	var (
		resp dto.RoomsRes
	)

	for _, room := range registry.RoomRegistry {
		// Filter rooms based on projectId and lotId if they're provided
		if (projectId == nil || room.ProjectId == *projectId) &&
			(lotId == nil || room.LotId == *lotId) {
			// Convert Unix timestamp to time.Time
			createdTime := time.Unix(room.Created, 0)

			// Format the time in yyyy/mm/dd hh:mm:ss (24-hour format)
			formattedTime := createdTime.Format("2006/01/02 15:04:05")

			// Create a Room DTO without the connections map
			roomDTO := dto.Room{
				CompanyId:   room.CompanyId,
				CompanyName: room.CompanyName,
				ProjectId:   room.ProjectId,
				LotId:       room.LotId,
				LotTitle:    room.LotTitle,
				Connections: len(room.Connections),
				Created:     formattedTime,
			}

			// Add the room to the response
			resp.Rooms = append(resp.Rooms, roomDTO)
		}
	}

	resp.Total = len(resp.Rooms)

	return resp
}

func ConnList(roomKey *string) (dto.ConnRes, error) {

	var (
		resp dto.ConnRes
		err  error
	)

	// Get room entry from room registry
	room, exists := registry.RoomRegistry[*roomKey]

	if !exists {
		err = errors.New("room_not_found")
		return resp, err
	}

	for _, conn := range room.Connections {
		// Convert Unix timestamp to time.Time
        createdTime := time.Unix(conn.Created, 0)

		// Format the time in yyyy/mm/dd hh:mm:ss (24-hour format)
		formattedTime := createdTime.Format("2006/01/02 15:04:05")

		// Create a Conn DTO
		connDTO := dto.Conn{
			UserUId:   conn.User.Uid,
			FirstName: conn.User.FirstName,
			LastName:  conn.User.LastName,
			Username:  conn.User.Username,
			UserLevel: conn.User.LevelId,
			Created:   formattedTime,
		}

		// Add the connection to the response
		resp.Connections = append(resp.Connections, connDTO)
	}

	resp.Total = len(resp.Connections)

	return resp, nil
}
