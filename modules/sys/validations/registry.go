package validations

import (
	"strings"

	authValidations "ps-socket-server/modules/auth/validations"
	dto "ps-socket-server/modules/sys/dto"
	consts "ps-socket-server/pkg/consts"
	apiResponse "ps-socket-server/pkg/response"
)

func ValidateRoomsReq(e *apiResponse.Errors, roomsReq *dto.RoomsReq) {
	authValidations.CheckProjectId(e, roomsReq.ProjectId, "project_id", false)
	authValidations.CheckLotId(e, roomsReq.LotId, "lot_id", false)
}

func ValidateConnReq(e *apiResponse.Errors, connReq *dto.ConnReq) {
	CheckRoomKey(e, connReq.<PERSON>ey, "room_key", true)
}

func CheckRoomKey(e *apiResponse.Errors, obj interface{}, key string, isRequired bool) {
	if obj == nil && isRequired {
		e.SetError(true, key, consts.REQUIRED)
		return
	}

	value, ok := obj.(string)

	if !ok {
		e.SetError(true, key, consts.MUST_STRING)
	} else if len(strings.TrimSpace(value)) == 0 {
		e.SetError(true, key, consts.REQUIRED)
	}
}