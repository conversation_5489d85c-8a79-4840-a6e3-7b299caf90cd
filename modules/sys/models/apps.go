package models

import (
	dba "ps-socket-server/pkg/db"
	helpers "ps-socket-server/pkg/helpers"
)

func GetAppKey(apiKey *string) (SysApps, error) {

	var app SysApps
	var err error
	//o := orm.NewOrm()

	//err = o.QueryTable(new(SysApps)).Filter("api_key", apiKey).One(&app)

	filter := map[string]interface{}{
		"api_key": *apiKey,
	}

	err = dba.QueryTableOne(&app, helpers.ToPtr("invalid_api_key"), filter, nil, nil, false, false, &app)

	if err != nil {
		return app, err
	}

	return app, nil
}
