package models

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
)

type SysApps struct {
	Id         int64 `orm:"pk;auto"`
	Name       string
	Info       *string `orm:"null"`
	ApiKey     string
	Validity   time.Time
	IsActive   bool      `orm:"default(true)"`
	Created    time.Time `orm:"auto_now_add;type(datetime)"`
	CreatedBy  int64
	Modified   *time.Time `orm:"auto_now;type(datetime)"`
	ModifiedBy *int64
}

func init() {
	orm.RegisterModel(new(SysApps))
}