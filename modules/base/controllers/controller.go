package controller

import (
	"errors"
	"net/http"

	auditModel "ps-socket-server/modules/audit/models"
	companyModel "ps-socket-server/modules/company/models"
	userModel "ps-socket-server/modules/users/models"
	consts "ps-socket-server/pkg/consts"
	response "ps-socket-server/pkg/response"

	beego "github.com/beego/beego/v2/server/web"
)

type BaseController struct {
	beego.Controller

	AuditLogs      []auditModel.SysAudits
	ApiKey         string
	Company        *companyModel.Company
	User           *userModel.UserLite
	Response       *response.Response
	ShouldLog      bool
	Ip             string
	ModuleId       consts.SysModules
	EntityId       int64
	UserActivityId int64
}

func (c *BaseController) GetRequestHeader(key string) (string, error) {
	value := c.Ctx.Input.Header(key)

	if value == "" {
		return "", errors.New(key + " header is missing or not defined")
	}

	return value, nil
}

func (c *BaseController) Prepare() {

	if apiKey := c.Ctx.Input.GetData("api_key"); apiKey != nil {
		if apiKey, ok := apiKey.(string); ok {
			c.ApiKey = apiKey
		}
	}

	if company := c.Ctx.Input.GetData("company"); company != nil {
		if company, ok := company.(companyModel.Company); ok {
			c.Company = &company
		}
	}

	if user := c.Ctx.Input.GetData("user"); user != nil {
		if user, ok := user.(userModel.UserLite); ok {
			c.User = &user
		}
	}
}

func (c *BaseController) ApiResponse(httpStatusCode int, errors interface{}, alerts []*response.Alert, data interface{}) *response.Response {

	for _, alert := range alerts {
		if alert.Mesg == string(consts.SOMETHING_WENT_WRONG) {
			httpStatusCode = http.StatusInternalServerError
			break
		}
	}

	response := response.Response{
		Status: response.Status{
			Code:   httpStatusCode,
			Alerts: alerts,
		},
		Data: data,
	}

	if errors != nil {
		response.Status.Errors = errors
	}

	/*
		accessToken, ok := c.Ctx.Input.GetData("access_token").(string)

		if ok && accessToken != "" {
			if accessToken != "" {
				response.AccessToken = accessToken
			}
		}
	*/

	c.Ctx.Output.SetStatus(http.StatusOK)

	c.Ctx.Output.Header("Content-Type", "application/json")

	c.Data["json"] = response

	c.ServeJSON()

	return &response
}

// Finish is called AFTER the API method has run, just before response is sent
func (c *BaseController) Finish() {
	if !c.ShouldLog {
		return
	}

	var responseData *response.Response
	if c.Response != nil {
		responseData = c.Response
	}

	var companyId *int64
	if c.Company != nil {
		companyId = &c.Company.Id
	} else if c.User != nil {
		companyId = &c.User.AccessTokenClaims.CompanyId
	}

	var proxyId *int64
	if c.User == nil {
		proxyId = nil
	} else if c.User.AccessTokenClaims.ProxyId == nil {
		proxyId = nil
	} else if *c.User.AccessTokenClaims.ProxyId == 0 {
		proxyId = nil
	} else {
		proxyId = c.User.AccessTokenClaims.ProxyId
	}

	var userId *int64

	if c.User == nil {
		userId = nil
	} else {
		userId = &c.User.AccessTokenClaims.UserId
	}

	go response.CreateAPILog(
		c.Controller,
		&c.AuditLogs,
		companyId,
		userId,
		&c.Ip,
		responseData,
		proxyId,
		int64(c.ModuleId),
		c.EntityId,
		c.UserActivityId,
	)

}

func (c *BaseController) SetIp(ip string) {
	c.Ip = ip
}
