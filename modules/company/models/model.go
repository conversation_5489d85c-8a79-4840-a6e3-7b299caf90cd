package models

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
)

type Company struct {
	Id               int64     `orm:"pk;auto"`
	Uid              string    `orm:"type(uuid)"`
	Name             string    `orm:"null"`
	Address1         *string   `orm:"null"`
	Address2         *string   `orm:"null"`
	CountryId        *int64    `orm:"null"`
	StateId          *int64    `orm:"null"`
	City             *string   `orm:"null"`
	Zipcode          *string   `orm:"null"`
	PhoneCountryCode *string   `orm:"null"`
	PhoneCityCode    *string   `orm:"null"`
	Phone            *string   `orm:"null"`
	CompanyEmail     *string   `orm:"null"`
	ContactUsEmail   *string   `orm:"null"`
	CompanyUrl       *string   `orm:"null" json:"company_url"`
	BiddingUrl       *string   `orm:"null"`
	ContactPerson    *string   `orm:"null"`
	RenewalName1     *string   `orm:"null"`
	RenewalEmail1    *string   `orm:"null"`
	RenewalName2     *string   `orm:"null"`
	RenewalEmail2    *string   `orm:"null"`
	SuperUserId      *int64    `orm:"null"`
	SuperAdminId     *int64    `orm:"null"`
	Created          time.Time `orm:"auto_now_add;type(datetime)"`
	CreatedBy        int64
	Modified         *time.Time `orm:"auto_now;type(datetime)"`
	ModifiedBy       *int64     `orm:"null"`
	IsDeleted        bool       `orm:"default(false)"`
	Deleted          *time.Time `orm:"null"`
	DeletedBy        *int64     `orm:"null"`
	PoweredBy        bool       `orm:"default(true)"`
}

func init() {
	orm.RegisterModel(new(Company))
}