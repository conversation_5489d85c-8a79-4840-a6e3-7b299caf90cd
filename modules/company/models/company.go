package models

import (
	dba "ps-socket-server/pkg/db"
	helpers "ps-socket-server/pkg/helpers"
)

func GetByOrigin(origin *string) (Company, error) {
	var (
		filter = map[string]interface{}{
			"bidding_url": *origin,
		}
		model Company
	)

	err := dba.QueryTableOne(new(Company), helpers.ToPtr("COMPANY_WITH_BIDDING_URL_NOT_FOUND"), filter, nil, nil, false, false, &model)
	
	if err != nil {
		return model, err
	}

	return model, nil
}