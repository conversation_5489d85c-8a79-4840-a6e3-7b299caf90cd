package controller

import (
	"encoding/json"
	"net/http"

	consts "ps-socket-server/pkg/consts"
	apiResponse "ps-socket-server/pkg/response"

	dto "ps-socket-server/modules/auth/dto"
	services "ps-socket-server/modules/auth/services"
	authValidations "ps-socket-server/modules/auth/validations"
	base "ps-socket-server/modules/base/controllers"
)

// AuthController operations for Auth
type AuthController struct {
	base.BaseController
}

func NewAuthController() *AuthController {
	return &AuthController{}
}

func (c *AuthController) Authentication() {

	var (
		req    dto.AuthReq
		errors apiResponse.Errors
		alerts apiResponse.Alerts
		resp   dto.AuthRes
	)

	// Create Audit Log
	c.ShouldLog = true

	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		alerts.SetAlert(string(consts.INVALID_JSON), consts.Toster)
		c.ApiResponse(http.StatusBadRequest, nil, alerts.ArrAlert, nil)
		return
	}

	authValidations.ValidateAuthReq(&errors, &req)
	if errors.ArrError != nil {
		c.ApiResponse(http.StatusBadRequest, errors.ArrError, nil, nil)
		return
	}

	resp, err := services.GenerateAuthKey(req.ProjectId, req.LotId, c.User)

	if err != nil {
		alerts.SetAlert(err.Error(), consts.Toster)
		c.ApiResponse(http.StatusBadRequest, nil, alerts.ArrAlert, nil)
		return
	}

	// Prepare Response
	alerts.SetAlert(string(consts.SUCCESS), consts.Toster)
	c.ApiResponse(http.StatusOK, nil, alerts.ArrAlert, resp)
}
