package validations

import (
	"reflect"
	"strings"

	dto "ps-socket-server/modules/auth/dto"
	consts "ps-socket-server/pkg/consts"
	apiResponse "ps-socket-server/pkg/response"
)

func ValidateAuthReq(e *apiResponse.Errors, authReq *dto.AuthReq) {
	CheckProjectId(e, authReq.ProjectId, "project_id", true)
	CheckLotId(e, authReq.LotId, "lot_id", true)
}

func CheckProjectId(e *apiResponse.Errors, obj interface{}, key string, isRequired bool) {

	if reflect.ValueOf(obj).IsNil() && isRequired {
		e.SetError(true, key, consts.REQUIRED)
		return
	}

	if !reflect.ValueOf(obj).IsNil() {
		value, ok := obj.(*string)

		if !ok {
			e.SetError(true, key, consts.MUST_STRING)
		} else if len(strings.TrimSpace(*value)) == 0 {
			e.<PERSON><PERSON>rror(true, key, consts.REQUIRED)
		}
	}
}

func CheckLotId(e *apiResponse.Errors, obj interface{}, key string, isRequired bool) {
	if reflect.ValueOf(obj).IsNil() && isRequired {
		e.SetError(true, key, consts.REQUIRED)
		return
	}

	if !reflect.ValueOf(obj).IsNil() {
		value, ok := obj.(*string)

		if !ok {
			e.SetError(true, key, consts.MUST_STRING)
		} else if len(strings.TrimSpace(*value)) == 0 {
			e.SetError(true, key, consts.REQUIRED)
		}
	}
}
