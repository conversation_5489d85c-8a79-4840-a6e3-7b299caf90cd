package models

import (
	dbo "ps-socket-server/pkg/db"
)

func VerifyParticipation(projectId *string, lotId *string, userUid string, userLevelId int64) bool {
	// Call below function
	// CREATE OR REPLACE FUNCTION public.fn_is_auc_valid_for_socket(p_project_id uuid, p_lot_id uuid, p_user_id uuid, p_level_id smallint)
	//		RETURNS boolean
	//		LANGUAGE plpgsql
	// 		AS $function$

	query := `SELECT fn_is_auc_valid_for_socket(?, ?, ?, ?) as is_valid_participation`
	IsValidParticipation := false

	err := dbo.RawQueryRow(&query, &IsValidParticipation, nil, projectId, lotId, userUid, userLevelId)

	if err != nil {
		panic(err)
	}

	return IsValidParticipation

}
