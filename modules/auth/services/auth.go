package services

import (
	"encoding/json"
	"time"

	dto "ps-socket-server/modules/auth/dto"
	userModel "ps-socket-server/modules/users/models"
	config "ps-socket-server/pkg/config"
	helpers "ps-socket-server/pkg/helpers"
	registry "ps-socket-server/pkg/registry"
)

// GenerateAuthKey creates an encrypted authentication key using project ID, lot ID and user ID
func GenerateAuthKey(projectId *string, lotId *string, user *userModel.UserLite) (dto.AuthRes, error) {

	var (
		verifiedUser registry.User
		resp         dto.AuthRes
	)

	/*
		if !authModel.VerifyParticipation(projectId, lotId, user.Uid, user.LevelId){
			return resp, errors.New("invalid_participation_or_lot_not_live")
		}
	*/

	// Create a data structure to hold the information
	authData := registry.AuthKey{
		UserId:  user.Id,
		UserUid: user.Uid,
		LotId:   *lotId,
	}

	// Convert to JSON
	jsonData, err := json.Marshal(authData)
	if err != nil {
		panic(err)
	}

	

	// Encrypt the data
	authKey, err := helpers.Encrypt(jsonData, []byte(config.Env.PwAesEncryptionKey), []byte(config.Env.PwAesIv))
	if err != nil {
		panic(err)
	}

	verifiedUser = registry.User{
		Id:          user.Id,
		Uid:         user.Uid,
		CompanyId:   user.CompanyId,
		RoleId:      user.RoleId,
		LevelId:     user.LevelId,
		FirstName:   *user.FirstName,
		LastName:    *user.LastName,
		Username:    user.Username,
		AccessToken: user.AccessToken,
		ProjectId:   *projectId,
		LotId:       *lotId,
		Created:     time.Now().Unix(),
	}

	registry.VerifiedUserRegistry[authKey] = verifiedUser

	resp = dto.AuthRes{
		AuthKey: authKey,
	}

	return resp, nil
}
