package helpers

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"

	"math"
	"math/rand"

	"reflect"

	"strconv"

	"strings"
	"time"

	"github.com/beego/beego/v2/server/web/context"
	"github.com/beego/i18n"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

func VerifyHashedData(hashed string, data string) error {
	err := bcrypt.CompareHashAndPassword([]byte(hashed), []byte(data))

	if err != nil {
		return errors.New("hashed error")
	}
	return nil
}

func GetIpAddress(c *context.Context) *string {
	ip := c.Input.IP()
	if forwardedFor := c.Input.Header("X-Forwarded-For"); forwardedFor != "" {
		ip = forwardedFor
	}
	return &ip
}

func HashData(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), 14)
	if err != nil {
		return "", err
	}
	return string(bytes), err
}

func TimeToString(currentTime time.Time) string {
	localTime := currentTime.Local()
	return localTime.Format("15-07-2006 03:04 PM")
}

func RandomCodeGenerateWithString(prefix string) string {
	rng := rand.New(rand.NewSource(time.Now().UnixNano()))
	randomDigits := rng.Intn(9000000000) + 1000000000
	randomString := strconv.Itoa(randomDigits)
	result := fmt.Sprintf("%s%s", prefix, randomString)
	result = strings.ToUpper(result)
	return result
}

func RandomIntCode(numDigits int) int {

	if numDigits < 1 {
		numDigits = 6
	}

	if numDigits > 10 {
		numDigits = 10
	}

	min := int(math.Pow10(numDigits - 1)) // Minimum value for the specified number of digits
	max := int(math.Pow10(numDigits)) - 1 // Maximum value for the specified number of digits

	rng := rand.New(rand.NewSource(time.Now().UnixNano()))
	randomDigits := rng.Intn(max-min+1) + min

	return randomDigits
}

func GetUuid() string {
	id := uuid.New()
	return id.String()
}

func MergeSlices[T any](slices ...[]T) []T {
	var merged []T
	for _, slice := range slices {
		merged = append(merged, slice...)
	}
	return merged
}

// CountOccurrencesGeneric counts how many times a value appears in a slice.
func CountOccurrencesGeneric(slice interface{}, value interface{}) (int, error) {
	s := reflect.ValueOf(slice)
	if s.Kind() != reflect.Slice {
		return 0, fmt.Errorf("expected a slice but got %s", s.Kind())
	}

	count := 0
	for i := 0; i < s.Len(); i++ {
		if reflect.DeepEqual(s.Index(i).Interface(), value) {
			count++
		}
	}
	return count, nil
}

func CreateValidationString(isField bool, key string, errorKey string, extraParam ...string) string {
	if len(extraParam) > 0 {
		return fmt.Sprintf("%s<#SEP_M#>field=label:%s<#SEP_D#>%s", strings.ToUpper(errorKey), strings.ToUpper(key), strings.Join(extraParam, "<#SEP_D#>"))
	}
	if !isField {
		return fmt.Sprintf("%s<#SEP_M#>%s", strings.ToUpper(errorKey), strings.Join(extraParam, "<#SEP_D#>"))
	}
	return fmt.Sprintf("%s<#SEP_M#>field=label:%s", strings.ToUpper(errorKey), strings.ToUpper(key))
}

func CreateErrorString(key string, extraParam ...string) string {
	if len(extraParam) > 0 {
		return fmt.Sprintf("%s<#SEP_M#>%s", strings.ToUpper(key), strings.Join(extraParam, "<#SEP_D#>"))
	}
	return fmt.Sprintf("%s", strings.ToUpper(key))
}

func ConvertSliceToDatabaseArray(data []int64) string {
	byteData, _ := json.Marshal(data)
	return "{" + string(byteData[1:len(byteData)-1]) + "}"
}

func ConverteDatabaseArrayToSlice(data string) []int64 {
	newString := data[1 : len(data)-1]
	return mapStringsToInt64(strings.Split(newString, ","), StringToInt64)
}

func mapStringsToInt64(slice []string, mapper func(string) int64) []int64 {
	result := make([]int64, len(slice))
	for i, s := range slice {
		result[i] = mapper(s)
	}
	return result
}

func StringToInt64(s string) int64 {
	value, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		return 0
	}
	return value
}

// Generic function to convert any type to a pointer
func ToPtr[T any](value T) *T {
	return &value
}

// Generic function to get decimals
func GetDecimals(in float64) int64 {
	str := strconv.FormatFloat(in, 'f', -1, 64)
	if strings.Contains(str, ".") {
		slStr := strings.Split(str, ".")
		return int64(len(slStr[len(slStr)-1]))
	}
	return 0
}

func InArray(nodeArr []int64, chkNode int64) bool {
	var isInArray = false
	for _, node := range nodeArr {
		if node == chkNode {
			isInArray = true
			break
		}
	}
	return isInArray
}

func GenRandEightDigitNumber() int64 {
	return int64(10000000 + rand.Intn(99999999-10000000))
}

func Int64ToString(data int64) string {
	return strconv.FormatInt(data, 10)
}

// func DecryptInt[T IntTypes](data string) T {
// 	decr, err := Decrypt(data)
// 	if err != nil {
// 		logger.LogError(nil, "", err.Error())
// 	}

// 	return T(StringToInt64(string(decr)))
// }

type IntTypes interface {
	int8 | int16 | int32 | int64
}

func ConvertUTCToTimeZone(utcTime time.Time, timeZone string) (time.Time, error) {
	// Load the location for the specified time zone
	loc, err := time.LoadLocation(timeZone)
	if err != nil {
		return time.Time{}, fmt.Errorf("invalid time zone: %v", err)
	}
	// Convert the UTC time to the specified time zone
	localTime := utcTime.In(loc)
	return localTime, nil
}

func ArrayUnique(arr []int64) []int64 {
	// Create a map to track unique elements
	uniqueMap := make(map[int64]bool)
	var result []int64

	// Loop through the array and add only unique elements to the result slice
	for _, item := range arr {
		if _, exists := uniqueMap[item]; !exists {
			uniqueMap[item] = true
			result = append(result, item)
		}
	}

	return result
}

/*
--key length

	16 -> AES-128
	24 -> AES-192
	32 -> AES-256
*/

func EncryptOld(text []byte, keys, ivs string) (string, error) {
	key := []byte(keys) // 32-byte key
	iv := []byte(ivs)
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}

	stream := cipher.NewCFBEncrypter(block, iv)
	ciphertext := make([]byte, len(text))
	stream.XORKeyStream(ciphertext, text)

	// Encode the ciphertext to Base64
	encodedCiphertext := base64.StdEncoding.EncodeToString(ciphertext)
	return encodedCiphertext, nil
}

func DecryptOld(encodedCiphertext string, keys, ivs string) ([]byte, error) {
	// Decode the Base64 encoded ciphertext
	key := []byte(keys) // 32-byte key
	iv := []byte(ivs)
	ciphertext, err := base64.StdEncoding.DecodeString(encodedCiphertext)
	if err != nil {
		return nil, err
	}

	if len(ciphertext) < aes.BlockSize {
		return nil, errors.New("ciphertext too short")
	}

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return nil, err
	}

	// Decrypt the ciphertext
	stream := cipher.NewCFBDecrypter(block, iv)
	stream.XORKeyStream(ciphertext, ciphertext)

	return ciphertext, nil
}

func GenerateUniqueCodeString(length int) string {
	charset := "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	result := make([]byte, length)
	for i := 0; i < length; i++ {
		result[i] = charset[rand.Intn(len(charset))]
	}
	return string(result)
}

// func ValidatePassword(password string) bool {
// 	// Regular expression to match the password complexity rules
// 	// var passwordRegex = `^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{8,}$`
// 	// var passwordRegex = `^(?=.*[a-zA-Z])(?=.*\d)(?=.*[\W_]).{8,}$`
// 	// re := regexp.MustCompile(passwordRegex)
// 	// return re.MatchString(password)

// 	if len(password) < 8 {
// 		return false
// 	}

// 	// Check for at least one letter
// 	hasLetter := false
// 	hasDigit := false
// 	hasSpecial := false

// 	for _, ch := range password {
// 		if unicode.IsLetter(ch) {
// 			hasLetter = true
// 		}
// 		if unicode.IsDigit(ch) {
// 			hasDigit = true
// 		}
// 		if !unicode.IsLetter(ch) && !unicode.IsDigit(ch) {
// 			hasSpecial = true
// 		}
// 	}

// 	// All conditions must be satisfied
// 	return hasLetter && hasDigit && hasSpecial
// }

func EncodeStr(data []byte) string {
	return base64.StdEncoding.EncodeToString(data)
}

func DecodeStr(data string) string {
	dData, _ := base64.StdEncoding.DecodeString(data)
	return string(dData)
}

func NumberFormat(number float64, decimals int, decimalSep string, thousandsSep string) string {
	neg := false
	if number < 0 {
		number = -number
		neg = true
	}
	dec := int(decimals)
	// Will round off
	str := fmt.Sprintf("%."+strconv.Itoa(dec)+"F", number)
	prefix, suffix := "", ""
	if dec > 0 {
		prefix = str[:len(str)-(dec+1)]
		suffix = str[len(str)-dec:]
	} else {
		prefix = str
	}
	sep := []byte(thousandsSep)
	n, l1, l2 := 0, len(prefix), len(sep)
	// thousands sep num
	c := (l1 - 1) / 3
	tmp := make([]byte, l2*c+l1)
	pos := len(tmp) - 1
	for i := l1 - 1; i >= 0; i, n, pos = i-1, n+1, pos-1 {
		if l2 > 0 && n > 0 && n%3 == 0 {
			for j := range sep {
				tmp[pos] = sep[l2-j-1]
				pos--
			}
		}
		tmp[pos] = prefix[i]
	}
	s := string(tmp)
	if dec > 0 {
		s += decimalSep + suffix
	}
	if neg {
		s = "-" + s
	}

	return s
}

func TruncateNumber(some float64, places int) float64 {
	pow := math.Pow(10, float64(places))
	return float64(int64(some*pow)) / pow
}

func Encrypt(text, key, iv []byte) (string, error) {
	if len(key) != 32 || len(iv) != aes.BlockSize {
		return "", errors.New("invalid key or IV size")
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	stream := cipher.NewCFBEncrypter(block, iv)
	ciphertext := make([]byte, len(text))
	stream.XORKeyStream(ciphertext, text)

	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

func Decrypt(encodedCiphertext string, key, iv []byte) ([]byte, error) {
	if len(key) != 32 || len(iv) != aes.BlockSize {
		return nil, errors.New("invalid key or IV size")
	}

	ciphertext, err := base64.StdEncoding.DecodeString(encodedCiphertext)
	if err != nil {
		return nil, err
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	stream := cipher.NewCFBDecrypter(block, iv)
	plaintext := make([]byte, len(ciphertext))
	stream.XORKeyStream(plaintext, ciphertext)

	return plaintext, nil
}

func GetDateTimeDiff(fromDate, toDate time.Time, purpose int) int64 {
	timeDiff := fromDate.Sub(toDate)
	var diff int64

	if purpose == 1 {
		diff = int64(timeDiff.Seconds())
	} else if purpose == 2 {
		diff = int64(timeDiff.Minutes())
	} else if purpose == 3 {
		diff = int64(timeDiff.Hours())
	}

	return diff
}

func TraslateKey(langCode string, key string) string {
	if langCode == "" {
		langCode = "en"
	}
	return i18n.Tr(langCode, key)
}

// func Encrypt(text, key, iv []byte) (string, error) {
// 	block, err := aes.NewCipher([]byte(key))
// 	if err != nil {
// 		return "", err
// 	}

// 	stream := cipher.NewCFBEncrypter(block, iv)
// 	ciphertext := make([]byte, len(text))
// 	stream.XORKeyStream(ciphertext, text)

// 	// Encode the ciphertext to Base64
// 	encodedCiphertext := base64.StdEncoding.EncodeToString(ciphertext)
// 	return encodedCiphertext, nil
// }

// func Decrypt(encodedCiphertext string, key, iv []byte) ([]byte, error) {
// 	// Decode the Base64 encoded ciphertext
// 	ciphertext, err := base64.StdEncoding.DecodeString(encodedCiphertext)
// 	if err != nil {
// 		return nil, err
// 	}

// 	if len(ciphertext) < aes.BlockSize {
// 		return nil, errors.New("ciphertext too short")
// 	}

// 	block, err := aes.NewCipher(key)
// 	if err != nil {
// 		return nil, err
// 	}

// 	// Decrypt the ciphertext
// 	stream := cipher.NewCFBDecrypter(block, iv)
// 	stream.XORKeyStream(ciphertext, ciphertext)

// 	return ciphertext, nil
// }

func GetCasesTitle(langCode, input string) string {
	var returnStr string

	switch langCode {
	case "en":
		returnStr = cases.Title(language.English).String(input)
	case "de":
		returnStr = cases.Title(language.German).String(input)
	case "fr":
		returnStr = cases.Title(language.French).String(input)
	case "it":
		returnStr = cases.Title(language.Italian).String(input)
	case "es":
		returnStr = cases.Title(language.Spanish).String(input)
	case "ar":
		returnStr = cases.Title(language.Arabic).String(input)
	case "pt":
		returnStr = cases.Title(language.Portuguese).String(input)
	case "ru":
		returnStr = cases.Title(language.Russian).String(input)
	case "zh":
		returnStr = cases.Title(language.Chinese).String(input)
	case "ja":
		returnStr = cases.Title(language.Japanese).String(input)
	default:
		returnStr = input // If language code is not recognized, return input as it is
	}
	return returnStr
}
