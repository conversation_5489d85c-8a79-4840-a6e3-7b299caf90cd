package consts

type ResponseMesgs string

const (
	SUCCESS                            ResponseMesgs = "success"
	INVALID_JSON                       ResponseMesgs = "invalid_json"
	SOMETHING_WENT_WRONG               ResponseMesgs = "something_went_wrong"
	NO_DATA_FOUND                      ResponseMesgs = "no_data_found"
	NO_PERMISSION                      ResponseMesgs = "no_permission"
	SYSTEM_ERROR                       ResponseMesgs = "system_error"
	NO_ROW                             ResponseMesgs = "NO_ROW"
	NO_INSERT                          ResponseMesgs = "NO_ROW_AFFECTED"
	NO_UPDATE                          ResponseMesgs = "NO_ROW_AFFECTED"
	NO_DELETE                          ResponseMesgs = "NO_ROW_AFFECTED"
	USER_NOT_ACTIVE                    ResponseMesgs = "user_not_active"
	PARTICIPANT_NOT_ACTIVE             ResponseMesgs = "participant_not_active"
	PARTICIPANT_NOT_INVITED            ResponseMesgs = "participant_not_invited"
	ITEM_EXCEEDED                      ResponseMesgs = "item_exceeded"
	ITEMS_NOT_CREATED                  ResponseMesgs = "items_not_created"
	INVALID_START_DATE                 ResponseMesgs = "invalid_start_date"
	INVALID_END_DATE                   ResponseMesgs = "invalid_end_date"
	CAN_NOT_ARCHIVE_AUCTION            ResponseMesgs = "can_not_archive_auction"
	CAN_NOT_AWARD_AUCTION              ResponseMesgs = "can_not_award_auction"
	APPROVE_PRIV_REMOVED               ResponseMesgs = "approve_priv_removed"
	REQUEST_ACCESS_DURATION_IS_INVALID ResponseMesgs = "request_access_duration_is_invalid"
	APPROVER_NOT_SELECTED              ResponseMesgs = "approver_not_selected"
	LOT_NOT_CREATED                    ResponseMesgs = "lot_not_created"
	AUCTION_EXCEEDED                   ResponseMesgs = "auction_exceeded"
	INVALID_DOC_TYPE                   ResponseMesgs = "invalid_doc_type"
	DOC_SIZE_EXCEED                    ResponseMesgs = "doc_size_exceed"
	BLOCK_BIDDING_NOT_ALLOWED          ResponseMesgs = "block_bidding_not_allowed"
	ALLOW_BIDDING_NOT_ALLOWED          ResponseMesgs = "allow_bidding_not_allowed"
	BID_ALREADY_INVALIDATED            ResponseMesgs = "bid_already_invalidated"
	DUPLICATE_AUCTION_REF_NO           ResponseMesgs = "duplicate_auction_ref_no"
	INVALID_OPT_OUT_DATE               ResponseMesgs = "invalid_opt_out_date"
)
