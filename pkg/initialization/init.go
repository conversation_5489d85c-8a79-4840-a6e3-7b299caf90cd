package initialization

import (
	"context"
	"fmt"
	"log"
	"os"

	config "ps-socket-server/pkg/config"
	consts "ps-socket-server/pkg/consts"
	db "ps-socket-server/pkg/db"
	glitchtip "ps-socket-server/pkg/glitchtip"
	logger "ps-socket-server/pkg/logger"
	registry "ps-socket-server/pkg/registry"

	beego "github.com/beego/beego/v2/server/web"
)

// Init initializes all components of the application
// It loads configurations, sets up GlitchTip/Sentry, initializes loggers,
// database connections, and registries in the correct order
// Parameter:
//   - ctx: context for AWS secret manager operations
//
// Returns an error if any initialization step fails
func Init(ctx context.Context) error {

	// orm.Debug = true

	/*
		if len(os.Args) > 1 {
			log.Println(os.Args[1])
		}
	*/

	// Load Local Configurations
	err := config.LoadConfig()
	if err != nil {
		return err
	}

	if len(os.Args) > 1 && os.Args[1] == string(consts.Dev) {
		beego.BConfig.RunMode = string(consts.Dev)
	} else if len(os.Args) > 1 {
		beego.BConfig.RunMode = os.Args[1]
		secretName := fmt.Sprintf("backend/%s", os.Args[1])

		err = config.LoadSecretManager(ctx, secretName, "us-east-1")
		if err != nil {
			return err
		}
	}

	log.Println("[1] Loaded Config, Environment :" + beego.BConfig.RunMode)

	// GlitchTip Sentry
	err = glitchtip.Init()
	if err != nil {
		return err
	}

	log.Println("[2] Initialize GlitchTip")

	//Initialize Logger
	err = logger.Init()
	if err != nil {
		return err
	}

	log.Println("[3] Initialize Logger")

	// Initialize DB and Connection Pooling
	err = db.Init()
	if err != nil {
		return err
	}

	log.Println("[4] Initialize DB.")

	// Initialize Registry
	registry.Init()
	registry.InitActions() // Initialize Action Map

	log.Println("[5] Initialize Registry.")

	return nil
}
