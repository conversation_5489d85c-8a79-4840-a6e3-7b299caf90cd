package config

import (
	"context"
	"encoding/json"
	"fmt"

	aws "github.com/aws/aws-sdk-go-v2/aws"
	awsConfig "github.com/aws/aws-sdk-go-v2/config"
	credentials "github.com/aws/aws-sdk-go-v2/credentials"
	secretsmanager "github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	mapstructure "github.com/mitchellh/mapstructure"

	"github.com/spf13/viper"
)

type Config struct {

	//Application
	AppBuildRelease string `mapstructure:"APP_BUILD_REALSE"`

	// Database configuration
	DbDriver     string `mapstructure:"DB_DRIVER"`
	DbCon        string `mapstructure:"DB_CON"`
	DbMaxOpenCon int    `mapstructure:"DB_MAX_OPEN_CON"`
	DbMaxIdleCon int    `mapstructure:"DB_MAX_IDLE_CON"`

	// Database Readonly
	DbReadCon        string `mapstructure:"DB_READ_CON"`
	DbReadMaxOpenCon int    `mapstructure:"DB_READ_MAX_OPEN_CON"`
	DbReadMaxIdleCon int    `mapstructure:"DB_READ_MAX_IDLE_CON"`

	//JWT Tokens
	JwtSecret           string `mapstructure:"JWT_SECRET"`
	AccessTokenTimeout  int64  `mapstructure:"ACCESS_TOKEN_TIMEOUT"`
	RefreshTokenTimeout int64  `mapstructure:"REFRESH_TOKEN_TIMEOUT"`

	// Encryption/Decryption Keys
	AesEncryptionKey string `mapstructure:"AES_ENCRYPTION_KEY"`
	AesIv            string `mapstructure:"AES_IV"`
	PwAesEncryptionKey string `mapstructure:"PW_AES_ENCRYPTION_KEY"`
	PwAesIv            string `mapstructure:"PW_AES_IV"`

	// AWS Secret Manager Configuration
	AwsSmAccessKey string `mapstructure:"AWS_SM_ACCESS_KEY"`
	AwsSmSecretKey string `mapstructure:"AWS_SM_SECRET_ACCESS_KEY"`

	//Storage
	StoragePath string `mapstructure:"STORAGE_PATH"`
	AppLogPath  string `mapstructure:"APP_LOG_PATH"`
	SysLogPath  string `mapstructure:"SYS_LOG_PATH"`

	//GlitchTip
	SentryDsn           string `mapstructure:"SENTRY_DSN"`
	SentryEnableTracing bool   `mapstructure:"SENTRY_ENABLE_TRACING"`
}

var Env Config

// LoadConfig loads configuration from the app.env file
// It reads the configuration file, parses it, and populates the Env global variable
// Returns an error if loading or parsing the configuration fails
func LoadConfig() error {

	viper.AddConfigPath(".")
	viper.SetConfigName("app")
	viper.SetConfigType("env")
	viper.AutomaticEnv()
	err := viper.ReadInConfig()
	if err != nil {
		return fmt.Errorf("failed to load config : %w", err)
	}
	err = viper.Unmarshal(&Env)

	if err != nil {
		return fmt.Errorf("failed to unmarshal config : %w", err)
	}

	return nil
}

func LoadSecretManager(ctx context.Context, secretName string, awsRegion string) error {

	creds := credentials.NewStaticCredentialsProvider(Env.AwsSmAccessKey, Env.AwsSmSecretKey, "")

	cfg, err := awsConfig.LoadDefaultConfig(ctx,
		awsConfig.WithCredentialsProvider(creds),
		awsConfig.WithRegion(awsRegion),
	)

	if err != nil {
		return fmt.Errorf("Failed to load aws config : %w\n", err)
	}

	// Create Secrets Manager client
	svc := secretsmanager.NewFromConfig(cfg)

	input := &secretsmanager.GetSecretValueInput{
		SecretId:     aws.String(secretName),
		VersionStage: aws.String("AWSCURRENT"), // VersionStage defaults to AWSCURRENT if unspecified
	}

	// Fetch Secret
	secret, err := svc.GetSecretValue(context.TODO(), input)
	if err != nil {
		// For a list of exceptions thrown, see
		// https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html

		return fmt.Errorf("Failed to get secret value : %w\n", err)

	}

	// Decode Secret Value
	var secretData map[string]interface{}
	if err := json.Unmarshal([]byte(*secret.SecretString), &secretData); err != nil {
		return fmt.Errorf("Failed to decode secret value : %w\n", err)
	}

	// Directly populate the Env struct from secretData
	if err := mapstructure.Decode(secretData, &Env); err != nil {
		return fmt.Errorf("failed to populate Env from secretData : %w\n", err)
	}

	return nil
}
