package logger

import (
	"errors"
	"fmt"
	"os"
	"runtime/debug"
	"time"

	config "ps-socket-server/pkg/config"
	consts "ps-socket-server/pkg/consts"

	logs "github.com/beego/beego/v2/core/logs"
	beego "github.com/beego/beego/v2/server/web"
	sentry "github.com/getsentry/sentry-go"
)

var log *logs.BeeLogger

func Init() error {

	//System Logger
	err := logs.SetLogger(logs.AdapterFile, `{"filename":"`+config.Env.SysLogPath+`/system.log",
											"maxlines":1000,"daily":true,
											"maxdays":31,"color":true,
											"level":7}`)

	if err != nil {
		return errors.New("Unable to config system logger: " + err.Error())
	}

	//Application's Logger

	log = logs.NewLogger(10000)

	err = log.SetLogger(logs.AdapterMultiFile, `{"filename":"`+config.Env.AppLogPath+`/app.log",
												"maxlines":1000,"daily":true,
												"maxdays":31,"color":true,
												"separate":["error", "info", "debug"]}`)

	if err != nil {
		return errors.New("Unable to config app logger: " + err.Error())
	}

	log.SetLevel(logs.LevelDebug) // Adjust as needed (LevelInfo, LevelWarn, etc.)

	// Enable asynchronous logging for better performance
	log = log.Async()

	return nil
}

func LogConsole(message string) {
	if beego.BConfig.RunMode == "dev" {
		fmt.Fprintln(os.Stdout, message)
	}
}

func LogInfo(mesg *string) {
	if beego.BConfig.RunMode == string(consts.Dev) {
		log.Informational(*mesg)
	} else if beego.BConfig.RunMode == string(consts.Sandbox) || beego.BConfig.RunMode == string(consts.Demo) {
		LogSentry(mesg, string(consts.Info))
	}

	LogConsole(*mesg)
}

// LogDebug logs a debug message
// Parameters:
//   - where: Where the log is called from, i.e. controller name :: service name :: model name
//   - message: The message to be logged
func LogDebug(where *string, message interface{}) {

	mesg := fmt.Sprintf("%s\n\n%v", *where, message)

	if beego.BConfig.RunMode == string(consts.Dev) {
		log.Debug(mesg)
	} else if beego.BConfig.RunMode == string(consts.Sandbox) || beego.BConfig.RunMode == string(consts.Demo) {
		LogSentry(&mesg, string(consts.Debug))
	} else if beego.BConfig.RunMode == string(consts.Staging) || beego.BConfig.RunMode == string(consts.Prod) {
		LogSentry(&mesg, string(consts.Debug))
	}

	LogConsole(mesg)
}

func LogPanic(message interface{}) {

	mesg := fmt.Sprintf("%v", message)

	if beego.BConfig.RunMode == string(consts.Dev) {
		log.Error(mesg)
	} else if beego.BConfig.RunMode == string(consts.Sandbox) || beego.BConfig.RunMode == string(consts.Demo) {
		LogSentry(&mesg, string(consts.Fatal))
	} else if beego.BConfig.RunMode == string(consts.Staging) || beego.BConfig.RunMode == string(consts.Prod) {
		LogSentry(&mesg, string(consts.Fatal))
	}

	LogConsole(mesg)
}

func LogException(err error, level consts.LogLevel) {

	stackTrace := string(debug.Stack())

	mesg := fmt.Sprintf("%v\n\n%s", err, stackTrace)

	if level == consts.Fatal {
		LogPanic(mesg)
	} else if level == consts.Error {
		LogPanic(mesg)
	} else if level == consts.Warning {
		LogPanic(mesg)
	} else if level == consts.Info {
		LogInfo(&mesg)
	} else if level == consts.Debug {
		LogDebug(&mesg, &mesg)
	}
}

// LogSentry sends a log message to Sentry for error tracking
// Parameters:
//   - mesg: pointer to the message to be sent to Sentry
//   - logType: pointer to the type of log (e.g., "error", "panic")
func LogSentry(mesg *string, level string) {

	event := sentry.NewEvent()

	event.Level = sentry.Level(level)

	event.Message = *mesg

	event.Tags = map[string]string{
		"platform": "ps-socket-server",
		"type":     level,
	}

	sentry.CaptureEvent(event)
	sentry.Flush(time.Second * 5)
}
