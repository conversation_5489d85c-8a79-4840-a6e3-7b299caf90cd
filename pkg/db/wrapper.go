package db

import (
	"errors"
	"fmt"
	"time"

	config "ps-socket-server/pkg/config"
	consts "ps-socket-server/pkg/consts"

	_ "github.com/lib/pq"

	orm "github.com/beego/beego/v2/client/orm"
)

// Init initializes the database connection and configuration
// It registers the database driver, sets up connection pools, and configures timeouts
// Returns an error if any part of the initialization fails
func Init() error {
	var err error

	err = orm.RegisterDriver(config.Env.DbDriver, orm.DRPostgres)
	if err != nil {
		return fmt.Errorf("failed to register driver: %w", err)
	}

	err = orm.RegisterDataBase(string(consts.DB_DEFAULT), config.Env.DbDriver, config.Env.DbCon)
	if err != nil {
		return fmt.Errorf("failed to register database: %w", err)
	}

	orm.SetMaxOpenConns(string(consts.DB_DEFAULT), config.Env.DbMaxOpenCon)
	orm.SetMaxIdleConns(string(consts.DB_DEFAULT), config.Env.DbMaxIdleCon)
	db, err := orm.GetDB(string(consts.DB_DEFAULT))

	if err != nil {
		return fmt.Errorf("failed to get database: %w", err)
	}

	db.SetConnMaxIdleTime(time.Duration(consts.DB_MAX_IDLE_TIME))
	db.SetConnMaxLifetime(time.Duration(consts.DB_MAX_LIFE_TIME))

	err = orm.RegisterDataBase(string(consts.DB_READ_ONLY), config.Env.DbDriver, config.Env.DbReadCon)
	if err != nil {
		return fmt.Errorf("failed to register database: %w", err)
	}

	orm.SetMaxOpenConns(string(consts.DB_READ_ONLY), config.Env.DbReadMaxOpenCon)
	orm.SetMaxIdleConns(string(consts.DB_READ_ONLY), config.Env.DbReadMaxIdleCon)
	db, err = orm.GetDB(string(consts.DB_READ_ONLY))

	if err != nil {
		return fmt.Errorf("failed to get database: %w", err)
	}

	db.SetConnMaxIdleTime(time.Duration(consts.DB_MAX_IDLE_TIME))
	db.SetConnMaxLifetime(time.Duration(consts.DB_MAX_LIFE_TIME))

	// Set to UTC time
	orm.DefaultTimeLoc = time.UTC
	err = setTimeZone()
	if err != nil {
		return fmt.Errorf("failed to set UTC time: %w", err)
	}

	return nil

}

// setTimeZone sets the database timezone to UTC
// Returns an error if setting the timezone fails
func setTimeZone() error {
	o := orm.NewOrmUsingDB(string(consts.DB_READ_ONLY))

	_, err := o.Raw("SET TIME ZONE 'UTC'").Exec()
	if err != nil {
		return err
	}

	return nil
}

func QueryTableOne[M any, R any](module *M, customError *string, filters map[string]interface{}, exclude map[string]interface{}, orderBy []string, isCount, isRel bool, resp *R) error {
	var err error
	var count int64
	o := orm.NewOrmUsingDB(string(consts.DB_READ_ONLY))

	// id, err :=
	query := o.QueryTable(module)
	if filters != nil {
		for key, value := range filters {
			query = query.Filter(key, value)
		}
	}

	if exclude != nil {
		for key, value := range exclude {
			query = query.Exclude(key, value)
		}
	}

	if len(orderBy) > 0 {
		query.OrderBy(orderBy...)
	}

	if isRel {
		query = query.RelatedSel()
	}

	if !isCount {
		err = query.One(resp)
	} else {
		count, err = query.Count()
		if err == nil {
			// Safely check and assign to the passed resp
			if respPtr, ok := any(resp).(*int64); ok {
				*respPtr = count // Assign count to resp if it's *int64
			} else if respPtr, ok := any(resp).(*int); ok {
				*respPtr = int(count) // Convert count to int and assign
			}
		}
	}

	if err != nil && err == orm.ErrNoRows {
		if customError != nil {
			return errors.New(*customError)
		} else {
			return nil
		}
	}

	if err != nil {
		panic(err)
	}
	return nil
}

// RawQueryRow executes a raw SQL query and scans a single row into the provided destination
// Parameters:
//   - query: pointer to the SQL query string
//   - rows: destination to scan the row into
//   - customError: optional custom error message to return on failure
//   - args: query arguments
//
// Returns an error if the query fails or no rows are found
func RawQueryRow(query *string, rows interface{}, customError *string, args ...interface{}) error {
	//var xyz T
	o := orm.NewOrmUsingDB(string(consts.DB_READ_ONLY))

	err := o.Raw(*query, args).QueryRow(rows)

	if err != nil && err == orm.ErrNoRows {
		if customError != nil {
			return errors.New(*customError)
		} else {
			return nil
		}
	}

	if err != nil {
		panic(err)
	}
	return nil
}

// RawQueryRow executes a raw SQL query and scans a single row into the provided destination
// Parameters:
//   - query: pointer to the SQL query string
//   - rows: destination to scan the row into
//   - customError: optional custom error message to return on failure
//   - args: query arguments
//
// Returns an error if the query fails or no rows are found
func RawQueryRows(query *string, rows interface{}, customError *string, args ...interface{}) error {
	//var xyz T
	o := orm.NewOrmUsingDB(string(consts.DB_READ_ONLY))

	_, err := o.Raw(*query, args).QueryRows(rows)

	if err != nil && err == orm.ErrNoRows {
		if customError != nil {
			return errors.New(*customError)
		} else {
			return nil
		}
	}

	if err != nil {
		panic(err)
	}
	return nil
}

// RawQueryValues executes a raw SQL query and returns the results as a map
// Parameters:
//   - query: pointer to the SQL query string
//   - values: pointer to store the query results
//   - needWhat: pointer to the key that must exist in the results
//   - customError: optional custom error message to return on failure
//   - args: query arguments
//
// Returns the number of records found and an error if the query fails or no rows are found
func RawQueryValues(query *string, values *[]orm.Params, needWhat *string, customError *string, args ...interface{}) (int64, error) {

	o := orm.NewOrmUsingDB(string(consts.DB_READ_ONLY))

	recs, err := o.Raw(*query, args).Values(values)

	if err == nil && (len(*values) <= 0 || (*values)[0][*needWhat] == nil) {
		err = orm.ErrNoRows
	}

	if err != nil && err == orm.ErrNoRows {
		if customError != nil {
			return 0, errors.New(*customError)
		} else {
			return 0, nil
		}
	}

	if err != nil {
		panic(err)
	}
	return recs, nil
}

// RawQueryObject executes a raw SQL query and scans a single row into the provided object
// Generic function that works with any type T
// Parameters:
//   - query: pointer to the SQL query string
//   - obj: pointer to the object to scan the row into
//   - customError: optional custom error message to return on failure
//   - args: query arguments
//
// Returns an error if the query fails or no rows are found
func RawQueryObject[T any](query *string, obj *T, customError *string, args ...interface{}) error {

	o := orm.NewOrmUsingDB(string(consts.DB_READ_ONLY))

	err := o.Raw(*query, args).QueryRow(obj)

	if err != nil && err == orm.ErrNoRows {
		if customError != nil {
			return errors.New(*customError)
		} else {
			return nil
		}
	}

	if err != nil {
		panic(err)
	}
	return nil
}
