package glitchtip

import (
	config "ps-socket-server/pkg/config"

	beego "github.com/beego/beego/v2/server/web"
	sentry "github.com/getsentry/sentry-go"
)

func Init() error {

	err := sentry.Init(sentry.ClientOptions{
		Dsn:           config.Env.SentryDsn,
		Environment:   beego.BConfig.RunMode,
		EnableTracing: config.Env.SentryEnableTracing,
		//Specify a fixed sample rate:
		//We recommend adjusting this value in production
		TracesSampleRate: 1.0,
		Release:          config.Env.AppBuildRelease,
	})

	if err != nil {
		return err
	}

	return nil
}
