package routers

import (
	syscontroller "ps-socket-server/modules/sys/controllers"
	middleware "ps-socket-server/pkg/middleware"

	beego "github.com/beego/beego/v2/server/web"
)

func ServeSys() {

	sys := syscontroller.NewSysController()

	nsSys := beego.NewNamespace("/v1",
		beego.NSNamespace("/sys",
			beego.NSBefore(middleware.PublicMiddleware),
			beego.NSBefore(middleware.OriginMiddleware),
			beego.NSNamespace("/health",
				beego.NSRouter("", sys, "get:Health"),
			),
			beego.NSNamespace("/rooms",
				beego.NSBefore(middleware.AuthMiddleware),
				beego.NSBefore(middleware.SysLevelMiddleware),
				beego.NSRouter("", sys, "post:RoomsList"),
			),
			beego.NSNamespace("/connections",
				beego.NSBefore(middleware.AuthMiddleware),
				beego.NSBefore(middleware.SysLevelMiddleware),
				beego.NSRouter("", sys, "post:ConnList"),
			),
		),
	)

	beego.AddNamespace(nsSys)
}
