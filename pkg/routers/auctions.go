package routers

import (
	auctionController "ps-socket-server/modules/auction/controllers"
	middleware "ps-socket-server/pkg/middleware"

	beego "github.com/beego/beego/v2/server/web"
)

func ServeAuction() {

	auction := auctionController.NewAuctionController()

	nsAuction := beego.NewNamespace("/v1",
		beego.NSNamespace("/auction",
			beego.NSBefore(middleware.PublicMiddleware),
			beego.NSBefore(middleware.OriginMiddleware),
			beego.NSNamespace("/change_config",
				beego.NSBefore(middleware.AuthMiddleware),
				beego.NSRouter("", auction, "post:ChangeConfig"),
			),
		),
	)

	beego.AddNamespace(nsAuction)
}
