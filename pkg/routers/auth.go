package routers

import (
	authController "ps-socket-server/modules/auth/controllers"
	middleware "ps-socket-server/pkg/middleware"

	beego "github.com/beego/beego/v2/server/web"
)

func ServeAuth() {

	auth := authController.NewAuthController()

	nsAuth := beego.NewNamespace("/v1",
		beego.NSNamespace("/auth",
			beego.NSBefore(middleware.PublicMiddleware),
			beego.NSBefore(middleware.OriginMiddleware),
			beego.NSBefore(middleware.AuthMiddleware),
			beego.NSNamespace("/do",
				beego.NSRouter("", auth, "post:Authentication"),
			),
		),
	)

	beego.AddNamespace(nsAuth)
}
