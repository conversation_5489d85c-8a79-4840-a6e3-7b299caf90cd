package routers

import (
	"net/http"

	controller "ps-socket-server/modules/websocket/controllers"

	beego "github.com/beego/beego/v2/server/web"
)

func ServeWebSocket() {
	wsController := controller.NewWebSocketController()

	// Register the WebSocket route with auth key as path parameter and filter
	beego.Router("/v1/ws", wsController, "get:HandleWebSocket")

	beego.SetStaticPath("/ws-client", "static/ws-client/index.html")
	beego.ErrorHandler("404", func(w http.ResponseWriter, r *http.Request) {
		http.Redirect(w, r, "/ws-client", http.StatusFound)
	})
}
