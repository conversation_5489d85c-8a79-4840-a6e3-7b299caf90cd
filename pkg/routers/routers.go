package routers

import (
	beego "github.com/beego/beego/v2/server/web"
	cors "github.com/beego/beego/v2/server/web/filter/cors"
)

func init() {
	corsOptions := cors.Options{
		AllowAllOrigins:  true,
		AllowOrigins:     []string{"http://localhost:8088", "http://localhost:8080", "https://procu.silicontechnolabs.com", "*"},                           // Allowed origins
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},                                                                              // Allowed methods
		AllowHeaders:     []string{"Origin", "Authorization", "Content-Type", "Accept", "Api-Key", "Token", "x-requested-with", "baggage", "sentry-trace"}, // Allowed headers
		ExposeHeaders:    []string{"Content-Length"},                                                                                                       // Exposed headers
		AllowCredentials: true,                                                                                                                             // Allow credentialstials
	}

	// Insert the CORS filter into Beego
	beego.InsertFilter("*", beego.BeforeRouter, cors.Allow(&corsOptions))

	ServeSys()

	ServeAuth()

	ServeWebSocket()

	ServeAuction()
}
