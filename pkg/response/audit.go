package response

import (
	"io"
	"time"

	auditModel "ps-socket-server/modules/audit/models"
	"ps-socket-server/pkg/helpers"

	beego "github.com/beego/beego/v2/server/web"
)

func CreateAPILog(c beego.Controller, audits *[]auditModel.SysAudits, companyId *int64, userId *int64, ipAddress *string, response *Response, proxyId *int64, moduleId int64, entityId int64, userActivityId int64) {

	request := c.Ctx.Request

	url := request.URL.String()
	userAgent := request.UserAgent()

	//Read Body
	var payload *string
	body, err := io.ReadAll(request.Body)
	if err == nil {
		if body != nil {
			payload = helpers.ToPtr(string(body))
		}
	}

	// Process Response
	// responseStr := ""
	// responseJSON, err := json.Marshal(response)
	// if err == nil {
	// 	responseStr = string(responseJSON)
	// }

	apiLog := auditModel.SysAudits{
		CompanyId:   companyId,
		UserId:      userId,
		ProxyUserId: proxyId,
		Url:         &url,
		IpAddress:   ipAddress,
		UserAgent:   &userAgent,
		Payload:     payload,
		//Response:      &responseStr,
		Created:       time.Now(),
		EntityId:      &entityId,
		ActivityLogId: int(userActivityId),
		ModuleId:      int(moduleId),
	}

	id := auditModel.Insert(&apiLog)

	if audits != nil && id != 0 {
		for _, audit := range *audits { // Dereference the pointer using *
			audit.ParentId = &id
			auditModel.Insert(&audit)
		}
	}

}

func CreateDataLog(audits *[]auditModel.SysAudits, entityType string, entityId int64, entityAction string, oldJson *string, newJson *string) {

	if audits == nil {
		audits = &[]auditModel.SysAudits{}
	}

	audit := auditModel.SysAudits{
		EntityType:   &entityType,
		EntityId:     &entityId,
		EntityAction: &entityAction,
		OldJson:      oldJson,
		NewJson:      newJson,
		Created:      time.Now(),
	}

	*audits = append(*audits, audit)
}
