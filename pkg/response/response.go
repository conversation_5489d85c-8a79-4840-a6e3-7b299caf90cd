package response

import (
	"encoding/json"
	"fmt"
	"net/http"
	"runtime/debug"
	"strings"

	consts "ps-socket-server/pkg/consts"
	helpers "ps-socket-server/pkg/helpers"
	logger "ps-socket-server/pkg/logger"

	beego "github.com/beego/beego/v2/server/web"
	context "github.com/beego/beego/v2/server/web/context"
)

// SetError Set error message for one field
// Function to add an error message
func (e *Errors) SetError(isField bool, key, mesg string, ectraParams ...string) {
	// Check if an Error object with the same key exists
	for _, err := range e.ArrError {
		if err.Key == key {
			// If the key exists, append the message to the existing object
			err.Mesg = append(err.Mesg, helpers.CreateValidationString(isField, key, mesg, ectraParams...))
			return
		}
	}
	newError := &Error{
		Mesg: []string{helpers.CreateValidationString(isField, key, mesg, ectraParams...)},
		Key:  key,
	}
	e.ArrError = append(e.ArrError, newError)
}

// SetAlert
func (a *Alerts) SetAlert(mesg string, displayType consts.DisplayType, extraParams ...string) {
	newAlert := &Alert{
		Mesg:    helpers.CreateErrorString(mesg, extraParams...),
		Display: displayType,
	}
	a.ArrAlert = append(a.ArrAlert, newAlert)
}

func MiddlewareResponse(ctx *context.Context, httpStatusCode int, errors interface{}, alerts interface{}, data interface{}) {

	if errors == nil {
		errors = []interface{}{} // Set empty errors array
	}

	if alerts == nil {
		alerts = []interface{}{} // Set empty errors array
	}

	response := Response{
		Status: Status{
			Code:   httpStatusCode,
			Errors: errors,
			Alerts: alerts,
		},
		Data: data,
	}

	ctx.Output.SetStatus(http.StatusOK)

	ctx.Output.Header("Content-Type", "application/json")
	resp, _ := json.Marshal(response)
	ctx.Output.Body(resp)

}

func (a *Alerts) SetAlerts(msgs []error, displayType consts.DisplayType) {
	for i := range msgs {
		msg := strings.Split(msgs[i].Error(), ",")
		if len(msg) > 1 {
			a.SetAlert(msg[0], displayType, msg[1:]...)
		} else {
			a.SetAlert(msg[0], displayType)
		}
	}
}

func (e *Errors) SetErrors(isField bool, key, mesg, display string, ectraParams ...string) {
	// Check if an Error object with the same key exists
	for _, err := range e.ArrError {
		if err.Key == key {
			// If the key exists, append the message to the existing object
			err.Mesg = append(err.Mesg, helpers.CreateValidationString(isField, key, mesg, ectraParams...))
			return
		}
	}
	newError := &Error{
		Mesg:    []string{helpers.CreateValidationString(isField, key, mesg, ectraParams...)},
		Key:     key,
		Display: display,
	}
	e.ArrError = append(e.ArrError, newError)
}

func WsResponse(httpStatusCode int, errors interface{}, alerts interface{}, data interface{}) Response {
	if errors == nil {
		errors = []interface{}{} // Set empty errors array
	}

	if alerts == nil {
		alerts = []interface{}{} // Set empty alerts array
	}

	response := Response{
		Status: Status{
			Code:   httpStatusCode,
			Errors: errors,
			Alerts: alerts,
		},
		Data: data,
	}

	return response
}

func CustomPanicHandler(ctx *context.Context, cfg *beego.Config) {
	if r := recover(); r != nil {
		errMsg := fmt.Sprintf("%s", r)

		// Check if this is a WebSocket template error
		// This happens, if error is already handled by WsPanicHandler
		// So need to bypass the CustomPanicHandler

		if strings.Contains(errMsg, "handlewebsocket.tpl") {
			return
		}

		// Get the stack trace as a string
		stackTrace := string(debug.Stack())

		err := fmt.Errorf("panic :: %s\n%s", r, stackTrace)

		logger.LogPanic(err)

		responses := Response{
			Status: Status{
				Code: http.StatusInternalServerError,
				Alerts: []Alert{
					{
						Display: consts.Toster,
						Mesg:    string(consts.SOMETHING_WENT_WRONG),
					},
				},
			},
		}

		resp, _ := json.Marshal(responses)
		ctx.Output.Header("Content-Type", "application/json")
		ctx.Output.SetStatus(http.StatusOK)
		ctx.Output.Body(resp)
	}
}
