package response

import (
	consts "ps-socket-server/pkg/consts"
)

// Single Message
type Error struct {
	Mesg    []string `json:"mesg"`
	Key     string   `json:"key"`
	Display string   `json:"display,omitempty"`
}

// Messages
type Errors struct {
	ArrError []*Error
}

// Single Alert
type Alert struct {
	Display consts.DisplayType `json:"display"`
	Mesg    string             `json:"mesg"`
}

// Messages
type Alerts struct {
	ArrAlert []*Alert
}

type Status struct {
	Code   int         `json:"code"`
	Errors interface{} `json:"errors,omitempty"`
	Alerts interface{} `json:"alerts,omitempty"`
}

type Response struct {
	Status Status      `json:"status"`
	Action string      `json:"action,omitempty"`
	Data   interface{} `json:"data,omitempty"`
	//AccessToken string      `json:"access_token"`
}
