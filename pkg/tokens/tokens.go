package tokens

import (
	"errors"
	"strings"
	"time"

	jwt "github.com/dgrijalva/jwt-go"
)

type AccessTokenClaims struct {
	ProxyId        *int64
	TokenType      string
	UserId         int64
	RoleId         int64
	LevelId        int64
	ParentRoleId   int64
	CompanyId      int64
	BiddingUrl     string
	ParticipantId  int64
	Privileges     string
	SubscriptionId int64
	jwt.StandardClaims
}

type RefreshTokenClaims struct {
	TokenType string
	UserId    int64
	jwt.StandardClaims
}

/*
*AccessTokenClaims
bool: isExpired
error
*/
func ValidateAccessToken(signedToken string, secret string) (*AccessTokenClaims, bool, error) {
	token, err := jwt.ParseWithClaims(
		signedToken,
		&AccessTokenClaims{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(secret), nil
		},
	)

	if err != nil && !strings.Contains(err.Error(), "token is expired") {
		return nil, true, err
	}

	claims, ok := token.Claims.(*AccessTokenClaims)
	if !ok {
		return nil, true, errors.New("access_token_invalid")
	}

	if claims.TokenType != "access" {
		return nil, true, errors.New("access_token_invalid")
	}

	if claims.ExpiresAt < time.Now().Unix() {
		return claims, true, nil
	}

	return claims, false, nil
}
