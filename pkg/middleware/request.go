package middleware

import (
	"errors"
	"net/http"

	companyModel "ps-socket-server/modules/company/models"
	sysService "ps-socket-server/modules/sys/services"
	userModel "ps-socket-server/modules/users/models"
	consts "ps-socket-server/pkg/consts"
	request "ps-socket-server/pkg/request"
	apiResponse "ps-socket-server/pkg/response"
	tokens "ps-socket-server/pkg/tokens"

	context "github.com/beego/beego/v2/server/web/context"
)

func PublicMiddleware(ctx *context.Context) {

	alerts := apiResponse.Alerts{}

	apiKey, err := VerifyApiKey(ctx)

	if err != nil {
		alerts.SetAlert(err.Error(), consts.Toster)
		apiResponse.MiddlewareResponse(ctx, http.StatusUnauthorized, nil, alerts.ArrAlert, nil)
		return
	}

	ctx.Input.SetData("api_key", apiKey)
}

func VerifyApiKey(ctx *context.Context) (string, error) {

	apiKey, err := request.GetRequestHeader(ctx, "Api-Key")

	if err != nil {
		return "", err
	}

	err = sysService.VerifyApp(&apiKey)

	if err != nil {
		return "", err
	}

	return apiKey, nil
}

/*
Chck company subscription is valid or not
*/
func OriginMiddleware(ctx *context.Context) {

	alerts := apiResponse.Alerts{}

	//Verify Company URL
	company, err := VerifyOrigin(ctx)

	if err != nil {
		alerts.SetAlert(err.Error(), consts.Toster)
		apiResponse.MiddlewareResponse(ctx, int(consts.CompanyNotRegistered), nil, alerts.ArrAlert, nil)
		return
	}

	if company.IsDeleted {
		alerts.SetAlert("COMPANY_DELETED", consts.Toster)
		apiResponse.MiddlewareResponse(ctx, int(consts.CompanyDeleted), nil, alerts.ArrAlert, nil)
		return
	}

	/*
		if company.Id != 0 {

			//Verify subscription
			subscription, err := VerifySubscription(company.Id)

			if err != nil {
				alerts.SetAlert(err.Error(), apiResponse.Toster)

				if subscription.Id == 0 {
					apiResponse.MiddlewareResponse(ctx, apiResponse.DoesNotSubscribed, nil, alerts.ArrAlert, nil)
				} else {
					apiResponse.MiddlewareResponse(ctx, apiResponse.SubscriptionExpiredOrFutureOrDeactivated, nil, alerts.ArrAlert, subscription)
				}

				return
			}
			ctx.Input.SetData("subscription", subscription)
		}
	*/

	ctx.Input.SetData("company", company)

}

func VerifyOrigin(ctx *context.Context) (companyModel.Company, error) {

	var company companyModel.Company

	origin, err := request.GetRequestHeader(ctx, "Origin")

	if err != nil {
		return company, err
	}

	company, err = companyModel.GetByOrigin(&origin)

	if err != nil {
		return company, err
	}

	return company, nil
}

func AuthMiddleware(ctx *context.Context) {

	VerifyAuth(ctx, 1)
}

/*
purpose: it is used to skip verifications
==========================================
0 = All verification are mandatory
1 = skip user verification & access token expiration check
*/
func VerifyAuth(ctx *context.Context, purpose int) {

	var accessTokenClaims *tokens.AccessTokenClaims

	secret := ctx.Input.GetData("api_key").(string)
	alerts := apiResponse.Alerts{}

	//Verify Access Token
	accessToken, accessTokenClaims, isAccessTokenExpired, err := VerifyAccessToken(ctx, secret)

	if err != nil {
		alerts.SetAlert("invalid_token", consts.Toster)
		apiResponse.MiddlewareResponse(ctx, int(consts.InvalidAccessToken), nil, alerts.ArrAlert, nil)
		return
	}

	if isAccessTokenExpired {
		alerts.SetAlert("Unauthorized", consts.Toster)
		apiResponse.MiddlewareResponse(ctx, http.StatusUnauthorized, nil, alerts.ArrAlert, nil)
		return
	}

	//Verify User Account
	user, err := userModel.GetUserLite(accessTokenClaims.UserId)

	if err != nil {
		alerts.SetAlert("invalid_token", consts.Toster)
		apiResponse.MiddlewareResponse(ctx, int(consts.InvalidAccessToken), nil, alerts.ArrAlert, nil)
		return
	}

	if user.AccessToken == "" || accessToken != user.AccessToken {
		err = errors.New("access_token_mismatch")
		alerts.SetAlert(err.Error(), consts.Toster)
		apiResponse.MiddlewareResponse(ctx, int(consts.InvalidAccessToken), nil, alerts.ArrAlert, nil)
		return
	}

	user.AccessTokenClaims = *accessTokenClaims

	company := ctx.Input.GetData("company").(companyModel.Company)

	if user.CompanyId != company.Id {
		err = errors.New("ws_company_mismatch")
		alerts.SetAlert(err.Error(), consts.Toster)
		apiResponse.MiddlewareResponse(ctx, int(consts.CompanyNotRegistered), nil, alerts.ArrAlert, nil)
		return
	}

	/*
		if purpose != 1 {

			refreshTokenClaims, isRefreshTokenExpired, err := tokens.ValidateRefreshToken(&user.RefreshToken, secret)

			if err != nil {
				alerts.SetAlert("invalid_token", apiResponse.None)
				apiResponse.MiddlewareResponse(ctx, apiResponse.InvalidRefreshToken, nil, alerts.ArrAlert, nil)
				return
			}

			if refreshTokenClaims != nil {
				user.RefreshTokenClaims = *refreshTokenClaims
			}

			if isRefreshTokenExpired {
				alerts.SetAlert("session_expired", apiResponse.Toster)
				apiResponse.MiddlewareResponse(ctx, apiResponse.RefreshTokenIsExpired, nil, alerts.ArrAlert, nil)
				return
			}

			if isAccessTokenExpired {
				accessTokenExpiry := time.Now().Add(time.Minute * time.Duration(config.Env.AccessTokenTimeout)).Unix()
				if accessTokenExpiry > refreshTokenClaims.ExpiresAt {
					accessTokenExpiry = refreshTokenClaims.ExpiresAt
				}
				// Generate new token
				secret := ctx.Input.GetData("api_key").(string)
				accessToken, err := tokens.GenerateAccessToken(accessTokenClaims.ProxyId, accessTokenClaims.UserId, accessTokenClaims.CompanyId, &accessTokenClaims.BiddingUrl, accessTokenClaims.ParticipantId, accessTokenClaims.RoleId, accessTokenClaims.ParentRoleId, accessTokenClaims.LevelId, accessTokenClaims.Privileges, accessTokenExpiry, secret, accessTokenClaims.SubscriptionId)

				if err != nil {
					alerts.SetAlert("Unauthorized", apiResponse.None)
					apiResponse.MiddlewareResponse(ctx, http.StatusUnauthorized, nil, alerts.ArrAlert, nil)
					return
				}

				user.AccessToken = accessToken
				err = userModel.UpdateAccessToken(&user.Id, &user.AccessToken)

				if err != nil {
					alerts.SetAlert("Unauthorized", apiResponse.None)
					apiResponse.MiddlewareResponse(ctx, http.StatusUnauthorized, nil, alerts.ArrAlert, nil)
					return
				}
				// Set Access Token Cookie
				ctx.Input.SetData("access_token", accessToken)
			}
		}
	*/

	ctx.Input.SetData("user", user)
}

func VerifyAccessToken(ctx *context.Context, secret string) (string, *tokens.AccessTokenClaims, bool, error) {

	// Get Access Token & Verify
	accessToken, err := request.GetRequestHeader(ctx, "Authorization")

	if err != nil {
		return "", nil, true, err
	}

	accessTokenClaims, isExpired, err := tokens.ValidateAccessToken(accessToken, secret)

	if err != nil {
		return "", nil, true, err
	}

	origin, err := request.GetRequestHeader(ctx, "Origin")

	if err != nil {
		return "", nil, true, err
	}

	if origin != accessTokenClaims.BiddingUrl {
		return "", nil, true, errors.New("access_token_invalid")
	}

	return accessToken, accessTokenClaims, isExpired, nil

}

func SysLevelMiddleware(ctx *context.Context) {

	alerts := apiResponse.Alerts{}

	user := ctx.Input.GetData("user").(userModel.UserLite)

	if user.LevelId != 1 {
		alerts.SetAlert("must_sys_level_user", consts.Toster)
		apiResponse.MiddlewareResponse(ctx, int(consts.INVALID_USER_LEVEL), nil, alerts.ArrAlert, nil)
		return
	}
}
