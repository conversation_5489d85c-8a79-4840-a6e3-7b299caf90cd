package registry

import "ps-socket-server/pkg/consts"

var ActionMap map[consts.ActionIn]consts.ActionOut

func InitActions() {
	ActionMap = make(map[consts.ActionIn]consts.ActionOut)

	ActionMap[consts.ECHO] = consts.NONE
	ActionMap[consts.CLOSED] = consts.NONE
	ActionMap[consts.NOTIFY] = consts.NOTIFICATION
	ActionMap[consts.BROADCAST] = consts.NOTIFICATION
	ActionMap[consts.NEW_BID] = consts.REFRESH
	ActionMap[consts.INVALIDATE_BID] = consts.REFRESH
	ActionMap[consts.CHANGE_CONFIG] = consts.REFRESH_CONFIG
}
