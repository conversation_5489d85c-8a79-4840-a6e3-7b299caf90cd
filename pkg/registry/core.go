package registry

import "github.com/gorilla/websocket"

type Auth<PERSON>ey struct {
	UserId  int64  `json:"user_id"`
	UserUid string `json:"user_uid"`
	LotId   string `json:"lot_id"`
}

// User model for storing user data.
type User struct {
	Id            int64  `json:"id"`
	Uid           string `json:"uid"`
	CompanyId     int64  `json:"company_id"`
	ParticipantId int64  `json:"participant_id,omitempty"`
	RoleId        int64  `json:"role_id"`
	LevelId       int64  `json:"level_id"`
	FirstName     string `json:"first_name"`
	LastName      string `json:"last_name"`
	Username      string `json:"username"`
	AccessToken   string `json:"access_token"`
	ProjectId     string `json:"project_id,omitempty"`
	LotId         string `json:"lot_id,omitempty"`
	Created       int64  `json:"created,omitempty"`
}

// WebSocketConnection model for storing WebSocket connections.
type WebSocketConnection struct {
	Conn    *websocket.Conn `json:"conn"`
	User    User            `json:"user"`
	Created int64           `json:"created,omitempty"`
}

// Room model for storing rooms and their connections.
type Room struct {
	CompanyId   string                         `json:"company_id"`
	CompanyName string                         `json:"company_name"`
	ProjectId   string                         `json:"project_id,omitempty"`
	LotId       string                         `json:"lot_id,omitempty"`
	LotTitle    string                         `json:"lot_title,omitempty"`
	Connections map[string]WebSocketConnection `json:"connections,omitempty"`
	Created     int64                          `json:"created,omitempty"`
}

var RoomRegistry map[string]Room

var VerifiedUserRegistry map[string]User
