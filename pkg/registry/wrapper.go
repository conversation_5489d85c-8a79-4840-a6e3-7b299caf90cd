package registry

import (
	"encoding/json"
	"errors"

	config "ps-socket-server/pkg/config"
	helpers "ps-socket-server/pkg/helpers"
)

// Init initializes the registry maps and creates a default system admin room
// This function must be called before using any registry functionality
func Init() {

	VerifiedUserRegistry = make(map[string]User)

	RoomRegistry = make(map[string]Room)

}

func ExtractRoom(authKeyStr *string, roomKey *string) (Room, error) {
	var (
		room    Room
		authKey AuthKey
		err error
	)

	if authKeyStr != nil {

		// Encrypt the data
		authKeyBytes, err := helpers.Decrypt(*authKeyStr, []byte(config.Env.PwAesEncryptionKey), []byte(config.Env.PwAesIv))
		if err != nil {
			panic(err)
		}

		err = json.Unmarshal(authKeyBytes, &authKey)
		if err != nil {
			panic(err)
		}

		// Create room key (ProjectId###LotId)
		roomKey = &authKey.LotId
	}

	// Check if room exists in RoomRegistry
	room, exists := RoomRegistry[*roomKey]

	if !exists {
		err = errors.New("room_not_found")
		return room, err
	}

	return room, nil
}
