2025/06/03 12:57:58.031  [E]  write error: websocket: close sent

goroutine 25 [running]:
runtime/debug.Stack()
	/usr/local/go/src/runtime/debug/stack.go:24 +0x64
ps-socket-server/pkg/logger.LogException({0x103869ca8, 0x140005aa5b0}, {0x10354be52, 0x5})
	/Users/<USER>/Documents/Projects/procure_port/ps-socket/ps-socket/pkg/logger/log.go:105 +0x2c
ps-socket-server/modules/websocket/services.WsResponse(0x1f4, {0x140004d6e10, 0x1, 0x1}, 0x1400004a638, {0x0, 0x0}, 0x140001b8420, 0x1)
	/Users/<USER>/Documents/Projects/procure_port/ps-socket/ps-socket/modules/websocket/services/service.go:105 +0x290
ps-socket-server/modules/websocket/controllers.(*WebSocketController).HandleWebSocket.(*WebSocketController).WsPanicHandler.func1.1()
	/Users/<USER>/Documents/Projects/procure_port/ps-socket/ps-socket/modules/websocket/controllers/socket_connection.go:87 +0x114
panic({0x1037184e0?, 0x103868790?})
	/usr/local/go/src/runtime/panic.go:770 +0x124
github.com/gorilla/websocket.(*Conn).NextReader(0x140001b8420)
	/Users/<USER>/go/pkg/mod/github.com/gorilla/websocket@v1.5.3/conn.go:1030 +0x1b0
github.com/gorilla/websocket.(*Conn).ReadMessage(0x103869ca8?)
	/Users/<USER>/go/pkg/mod/github.com/gorilla/websocket@v1.5.3/conn.go:1093 +0x1c
ps-socket-server/modules/websocket/controllers.(*WebSocketController).handleWebSocketConnection(0x0?, 0x140001b8420)
	/Users/<USER>/Documents/Projects/procure_port/ps-socket/ps-socket/modules/websocket/controllers/socket_connection.go:31 +0x44
ps-socket-server/modules/websocket/controllers.(*WebSocketController).HandleWebSocket.(*WebSocketController).WsPanicHandler.func1(0x1037c5320?)
	/Users/<USER>/Documents/Projects/procure_port/ps-socket/ps-socket/modules/websocket/controllers/socket_connection.go:117 +0x50
ps-socket-server/modules/websocket/controllers.(*WebSocketController).HandleWebSocket(0x14000206000)
	/Users/<USER>/Documents/Projects/procure_port/ps-socket/ps-socket/modules/websocket/controllers/handle_connection.go:83 +0x538
reflect.Value.call({0x10385ec40?, 0x14000206000?, 0x14000259398?}, {0x10354b4d6, 0x4}, {0x103e87a20, 0x0, 0x1034cc1ec?})
	/usr/local/go/src/reflect/value.go:596 +0x970
reflect.Value.Call({0x10385ec40?, 0x14000206000?, 0x1036e2e4d?}, {0x103e87a20?, 0x103709520?, 0x140002024b0?})
	/usr/local/go/src/reflect/value.go:380 +0x94
github.com/beego/beego/v2/server/web.(*ControllerRegister).serveHttp(0x140002ce1c0, 0x140002024b0)
	/Users/<USER>/go/pkg/mod/github.com/beego/beego/v2@v2.3.8/server/web/router.go:1234 +0x1668
github.com/beego/beego/v2/server/web.(*FilterRouter).filter(0x14000287dd0, 0x140002024b0, {0x1400024c0c4, 0x6}, 0x14000259b18?)
	/Users/<USER>/go/pkg/mod/github.com/beego/beego/v2@v2.3.8/server/web/filter.go:83 +0x1a4
github.com/beego/beego/v2/server/web.(*ControllerRegister).ServeHTTP(0x140002ce1c0, {0x103871a58, 0x140002ce000}, 0x14000001320)
	/Users/<USER>/go/pkg/mod/github.com/beego/beego/v2@v2.3.8/server/web/router.go:1003 +0xe8
net/http.serverHandler.ServeHTTP({0x14000202240?}, {0x103871a58?, 0x140002ce000?}, 0x6?)
	/usr/local/go/src/net/http/server.go:3137 +0xbc
net/http.(*conn).serve(0x140001cc240, {0x103872a68, 0x14000500630})
	/usr/local/go/src/net/http/server.go:2039 +0x508
created by net/http.(*Server).Serve in goroutine 36
	/usr/local/go/src/net/http/server.go:3285 +0x3f0

2025/06/03 13:18:53.263  [E]  messageType :: -1, message :: , read error:: websocket: close 1001 (going away)

goroutine 66 [running]:
runtime/debug.Stack()
	/usr/local/go/src/runtime/debug/stack.go:24 +0x64
ps-socket-server/pkg/logger.LogException({0x104dd5ca8, 0x140000bc410}, {0x104ab7e52, 0x5})
	/Users/<USER>/Documents/Projects/procure_port/ps-socket/ps-socket/pkg/logger/log.go:105 +0x2c
ps-socket-server/modules/websocket/controllers.(*WebSocketController).handleWebSocketConnection(0x0?, 0x140002cc6e0)
	/Users/<USER>/Documents/Projects/procure_port/ps-socket/ps-socket/modules/websocket/controllers/socket_connection.go:37 +0x44c
ps-socket-server/modules/websocket/controllers.(*WebSocketController).HandleWebSocket.(*WebSocketController).WsPanicHandler.func1(0x104d31320?)
	/Users/<USER>/Documents/Projects/procure_port/ps-socket/ps-socket/modules/websocket/controllers/socket_connection.go:116 +0x50
ps-socket-server/modules/websocket/controllers.(*WebSocketController).HandleWebSocket(0x140000fe140)
	/Users/<USER>/Documents/Projects/procure_port/ps-socket/ps-socket/modules/websocket/controllers/handle_connection.go:83 +0x538
reflect.Value.call({0x104dcac40?, 0x140000fe140?, 0x1400013d398?}, {0x104ab74d6, 0x4}, {0x1053f3a20, 0x0, 0x104a381ec?})
	/usr/local/go/src/reflect/value.go:596 +0x970
reflect.Value.Call({0x104dcac40?, 0x140000fe140?, 0x104c4ee4d?}, {0x1053f3a20?, 0x104c75520?, 0x140000ca1b0?})
	/usr/local/go/src/reflect/value.go:380 +0x94
github.com/beego/beego/v2/server/web.(*ControllerRegister).serveHttp(0x140002461c0, 0x140000ca1b0)
	/Users/<USER>/go/pkg/mod/github.com/beego/beego/v2@v2.3.8/server/web/router.go:1234 +0x1668
github.com/beego/beego/v2/server/web.(*FilterRouter).filter(0x1400021dd40, 0x140000ca1b0, {0x140001c8004, 0x6}, 0x1400013db18?)
	/Users/<USER>/go/pkg/mod/github.com/beego/beego/v2@v2.3.8/server/web/filter.go:83 +0x1a4
github.com/beego/beego/v2/server/web.(*ControllerRegister).ServeHTTP(0x140002461c0, {0x104ddda58, 0x14000246000}, 0x14000580120)
	/Users/<USER>/go/pkg/mod/github.com/beego/beego/v2@v2.3.8/server/web/router.go:1003 +0xe8
net/http.serverHandler.ServeHTTP({0x140000ca090?}, {0x104ddda58?, 0x14000246000?}, 0x6?)
	/usr/local/go/src/net/http/server.go:3137 +0xbc
net/http.(*conn).serve(0x140000dc120, {0x104ddea68, 0x14000451e90})
	/usr/local/go/src/net/http/server.go:2039 +0x508
created by net/http.(*Server).Serve in goroutine 45
	/usr/local/go/src/net/http/server.go:3285 +0x3f0

