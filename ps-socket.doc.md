# PS-Socket Documentation Reference

This documentation provides a comprehensive guide to the ps-socket application architecture and components.

## Directory Structure

```
ps-socket-server/
├── app.env              # Environment configuration file
├── conf/                # Beego configuration
│   └── app.conf         # Beego app configuration
├── go.mod               # Go modules file
├── logs/                # Application logs
│   ├── app-logs/        # Application-specific logs
│   └── sys-logs/        # System logs
├── main.go              # Entry point
├── modules/             # Feature modules
│   ├── audit/           # Audit functionality
│   ├── auth/            # Authentication functionality
│   ├── base/            # Base components
│   ├── company/         # Company functionality
│   ├── sys/             # System functionality
│   └── users/           # User functionality
└── pkg/                 # Shared packages
    ├── config/          # Configuration handling
    ├── consts/          # Constants
    ├── db/              # Database connections
    ├── glitchtip/       # Error tracking
    ├── helpers/         # Helper functions
    ├── initialization/  # App initialization
    ├── logger/          # Logging functionality
    ├── middleware/      # HTTP middleware
    ├── registry/        # Registry for app components
    ├── request/         # Request handling
    ├── response/        # Response handling
    ├── routers/         # API routes
    └── tokens/          # Token handling
```

## Router Definition

Routers are defined in the `pkg/routers` directory. The application uses <PERSON><PERSON>'s namespace feature to organize routes hierarchically.

### Example Router Definition

```go
// From pkg/routers/auth.go
func ServeAuth() {
    auth := authController.NewAuthController()

    nsAuth := beego.NewNamespace("/v1",
        beego.NSNamespace("/auth",
            beego.NSBefore(middleware.PublicMiddleware),
            beego.NSBefore(middleware.OriginMiddleware),
            beego.NSBefore(middleware.AuthMiddleware),
            beego.NSNamespace("/do",
                beego.NSRouter("", auth, "post:Authentication"),
            ),
        ),
    )

    beego.AddNamespace(nsAuth)
}
```

### Key Points About Routers

- Routes are organized in a hierarchical structure using Beego's namespace feature
- Middleware can be applied at different namespace levels
- Controller methods are mapped to specific HTTP methods (e.g., `"post:Authentication"`)
- Router initialization happens during application startup
- Each module typically has its own router file in the `pkg/routers` directory
- The router files are imported in the main package with a blank import (`_ "ps-socket-server/pkg/routers"`)

## API Request/Response Handling

The application uses a controller-based approach for handling API requests and responses:

### Request Flow

1. Request arrives at a defined endpoint
2. Middleware processes the request (authentication, origin check, etc.)
3. Controller method is invoked based on the route mapping
4. Request body is parsed and validated
5. Business logic is executed via service layer
6. Response is formatted and returned to the client

### BaseController

Located in `modules/base/controllers/controller.go`, the BaseController provides common functionality for all controllers:

- Header extraction with `GetRequestHeader`
- Preparation of controller context with `Prepare`
- Standardized API response formatting with `ApiResponse`
- Audit logging setup in `Finish`

```go
// Example of BaseController usage
func (c *BaseController) ApiResponse(httpStatusCode int, errors interface{}, alerts []*response.Alert, data interface{}) *response.Response {
    // Format and send API response
    // ...
}
```

### Controller Implementation

Controllers extend the BaseController and implement specific endpoint handlers:

```go
// From modules/auth/controllers/controller.go
func (c *AuthController) Authentication() {
    var (
        req    dto.AuthReq
        errors apiResponse.Errors
        alerts apiResponse.Alerts
        resp   dto.AuthRes
    )

    // Create Audit Log
    c.ShouldLog = true

    request.GetRequestBody(c.Ctx)

    if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
        log.Println(err)
        alerts.SetAlert(string(consts.INVALID_JSON), consts.Toster)
        c.ApiResponse(http.StatusBadRequest, nil, alerts.ArrAlert, nil)
        return
    }

    // Validate request, process it, and return response
    // ...
}
```

### Response Structure

The application uses a standardized response structure:

```go
// From pkg/response
type Response struct {
    Status      Status      `json:"status"`
    Data        interface{} `json:"data,omitempty"`
    AccessToken string      `json:"access_token,omitempty"`
}

type Status struct {
    Code   int         `json:"code"`
    Errors interface{} `json:"errors,omitempty"`
    Alerts []*Alert    `json:"alerts,omitempty"`
}
```

## The Purpose of `pkg`

The `pkg` directory contains shared packages that provide core functionality used across the entire application:

### Key Packages

- **config**: Loads and manages application configuration from app.env and AWS Secret Manager
- **consts**: Defines application-wide constants for consistent usage
- **db**: Provides database connection pooling and query utilities
- **glitchtip**: Integrates with GlitchTip/Sentry for error tracking
- **initialization**: Manages the application initialization sequence
- **logger**: Provides structured logging with different log levels
- **middleware**: Contains HTTP middleware for authentication, etc.
- **registry**: Maintains a registry of application components
- **request**: Utilities for processing HTTP requests
- **response**: Standardizes API response formatting
- **routers**: Defines API routes and connects them to controllers
- **tokens**: Handles JWT token generation and validation

These packages are designed to be reusable components that modules can depend on, keeping core application logic separate from business logic in modules.

## The Purpose of Modules

Modules represent discrete functional areas of the application, each with its own set of controllers, models, services, and DTOs:

### Module Structure

```
modules/
├── audit/           # Handles audit logging
├── auth/            # Authentication and authorization
│   ├── controllers/ # HTTP request handlers
│   ├── dto/         # Data Transfer Objects for request/response
│   ├── models/      # Database models
│   ├── services/    # Business logic implementation
│   └── validations/ # Input validation
├── base/            # Base components and shared functionality
├── company/         # Company management functionality
├── sys/             # System-level functionality
└── users/           # User management functionality
```

### Benefits of Modular Structure

- **Separation of Concerns**: Each module focuses on a specific functional area
- **Independent Development**: Teams can work on different modules concurrently
- **Code Organization**: Clear structure makes navigation and maintenance easier
- **Pattern Reuse**: Common patterns (controllers, services, etc.) are applied across modules
- **Scalability**: New features can be added as new modules without affecting existing code

## How app.env Works

The `app.env` file contains environment-specific configuration values that are loaded during application initialization:

### Configuration Loading

```go
// From initialization/init.go
err := config.LoadConfig()
if err != nil {
    return err
}

// Load environment-specific configuration from AWS Secret Manager in non-dev environments
if len(os.Args) > 1 && os.Args[1] == string(consts.Dev) {
    beego.BConfig.RunMode = string(consts.Dev)
} else if len(os.Args) > 1 {
    beego.BConfig.RunMode = os.Args[1]
    secretName := fmt.Sprintf("backend/%s", os.Args[1])

    err = config.LoadSecretManager(ctx, secretName, "us-east-1")
    if err != nil {
        return err
    }
}
```

### Configuration Categories

The app.env file is organized into sections:

```
# APP 
APP_BUILD_REALSE=0.1

# Database
DB_DRIVER="postgres"
DB_CON="user=postgres password=1234 dbname=procure_db host=************* sslmode=disable"
DB_MAX_OPEN_CON=2
DB_MAX_IDLE_CON=1

# Database Read Replica
DB_READ_CON="user=postgres password=1234 dbname=procure_db host=************* sslmode=disable"
DB_READ_MAX_OPEN_CON=2
DB_READ_MAX_IDLE_CON=1

# JWT Token
JWT_SECRET="procuresuit"

# Local Storage
STORAGE_PATH="../procuresuit_storage/temp"
APP_LOG_PATH="./logs/app-logs"
SYS_LOG_PATH="./logs/sys-logs"

# Encryption/Decryption Keys
AES_ENCRYPTION_KEY = "Ps4wvF3AAWmN4lbIxuaiG0uNjRO4iGDv" 
AES_IV = "PEgQclKNasLzYMLf"
```

## How Logger Works

The logger is initialized during application startup in the initialization sequence:

### Logger Initialization

```go
// From initialization/init.go
err = logger.Init()
if err != nil {
    return err
}
```

### Logging Features

- **Multiple Log Levels**: The logger supports different log levels (debug, info, error)
- **File-Based Logging**: Logs are written to separate files based on level
- **Structured Format**: Logs follow a consistent format for easier parsing and analysis
- **Configuration via app.env**: Log paths are configured in app.env

```
APP_LOG_PATH="./logs/app-logs"
SYS_LOG_PATH="./logs/sys-logs"
```

### Log Files

- `logs/app-logs/app.debug.log` - Detailed debug information
- `logs/app-logs/app.error.log` - Error logs
- `logs/app-logs/app.info.log` - Informational logs
- `logs/app-logs/app.log` - Combined logs
- `logs/sys-logs/system.log` - System-level logs

## How DB Connection Pooling Works

The application implements database connection pooling to efficiently manage database connections:

### Connection Pool Configuration

```go
// From pkg/db/wrapper.go
orm.SetMaxOpenConns(string(consts.DB_DEFAULT), config.Env.DbMaxOpenCon)
orm.SetMaxIdleConns(string(consts.DB_DEFAULT), config.Env.DbMaxIdleCon)
db, err := orm.GetDB(string(consts.DB_DEFAULT))

db.SetConnMaxIdleTime(time.Duration(consts.DB_MAX_IDLE_TIME))
db.SetConnMaxLifetime(time.Duration(consts.DB_MAX_LIFE_TIME))
```

### Key Features

- **Separate Read/Write Pools**: The application maintains separate connection pools for read and write operations
- **Configurable Pool Sizes**: Pool sizes are specified in app.env
  ```
  DB_MAX_OPEN_CON=2  # Maximum number of open connections
  DB_MAX_IDLE_CON=1  # Maximum number of idle connections
  
  DB_READ_MAX_OPEN_CON=2
  DB_READ_MAX_IDLE_CON=1
  ```
- **Connection Lifetime Management**: Maximum idle time and lifetime for connections
- **Database Driver**: PostgreSQL is used as the database driver
- **Connection Timeout Handling**: Prevents resource leaks by timing out idle connections

### Database Query Utilities

The `pkg/db/wrapper.go` file provides several utility functions for database operations:

- `QueryTableOne`: Executes object-relational mapping queries
- `RawQueryRow`: Executes raw SQL queries for single rows
- `RawQueryValues`: Executes raw SQL queries returning multiple values
- `RawQueryObject`: Executes raw SQL queries for a single object

## Registry System

The registry system (in `pkg/registry`) maintains references to application components:

```go
// From initialization/init.go
// Initialize Registry
registry.Init()
```

### Purpose

- Manages application-wide state and component references
- Provides a centralized location for component access
- Maintains room registry for the socket server functionality
- Facilitates communication between modules without direct dependencies

## Initialization Sequence

The application follows a structured initialization sequence in `pkg/initialization/init.go`:

1. **Load Configuration**: Reads app.env and/or AWS Secret Manager
2. **Initialize Error Tracking**: Sets up GlitchTip/Sentry integration
3. **Initialize Logger**: Sets up logging system and files
4. **Initialize Database**: Establishes connection pools
5. **Initialize Registry**: Sets up component registry

This sequence ensures that components are initialized in the correct order, with dependencies available when needed.

## WebSocket Implementation

The ps-socket application implements WebSocket functionality for real-time bidirectional communication between clients and the server. This implementation uses the gorilla/websocket package and is integrated with the existing authentication system.

### WebSocket Authentication Flow

1. **Authentication Request**: Client makes a request to `/v1/auth/do` API with:
   - Headers: Api-Key, Origin, Authorization
   - Body: project_id, lot_id, ip
   
2. **Validation**:
   - Api-Key validation using PublicMiddleware
   - Origin validation using OriginMiddleware
   - User authentication using AuthMiddleware
   
3. **Auth Key Generation**:
   - Server generates a unique AuthKey using ProjectId, LotId, user ID, and timestamp
   - Creates a VerifiedUser instance with user details and project/lot information
   - Adds this instance to VerifiedUserRegistry map with AuthKey as key
   - Returns AuthKey to client
   
4. **WebSocket Connection**:
   - Client connects to `/v1/ws/:authkey` WebSocket endpoint with AuthKey as path parameter
   - Server validates AuthKey against VerifiedUserRegistry
   - If valid, server upgrades the connection to WebSocket protocol
   - Creates a WebSocketConnection instance with connection, user details, and timestamp
   
5. **Room Management**:
   - Server checks for existing room with matching ProjectId and LotId in RoomRegistry
   - If room doesn't exist, creates a new room
   - Adds WebSocketConnection to the room's connections map
   - Removes VerifiedUser from VerifiedUserRegistry after successful connection

### WebSocket Communication Handling

The WebSocket implementation includes robust error handling and message processing:

1. **Connection Handling**:
   - The `HandleWebSocket` controller method processes initial connection requests and performs authentication
   - Upon successful authentication, it passes the connection to `handleWebSocketConnection` wrapped with `WSPanicHandler` for safety

2. **Message Processing**:
   - The `handleWebSocketConnection` function manages the WebSocket connection lifecycle
   - It reads messages from the client in a continuous loop
   - Messages are parsed from JSON format
   - Processed messages are sent back to the client
   - Various error conditions are logged using the application's logging system

3. **Error Recovery**:
   - The `WSPanicHandler` wrapper provides panic recovery for WebSocket handlers
   - If a panic occurs, it:
     - Captures the stack trace
     - Logs the error through the logging system
     - Attempts to gracefully close the WebSocket connection with an appropriate status code
     - Prevents application crashes due to errors in WebSocket handlers

### Key Components

#### Registry Structures
```go
// WebSocketConnection model
type WebSocketConnection struct {
    Conn    *websocket.Conn
    User    User
    Created int64
}

// Room model
type Room struct {
    ProjectId   string
    LotId       string
    Connections map[string]WebSocketConnection
    Created     int64
}

// Registration maps
var RoomRegistry map[string]Room
var VerifiedUserRegistry map[string]VerifiedUser
```

#### WebSocket Controller
The WebSocketController handles WebSocket connection requests:
- `HandleWebSocket`: Entry point for WebSocket connections, validates AuthKey
- `handleWebSocketConnection`: Manages the WebSocket lifecycle and message processing

#### WebSocket Panic Recovery
```go
// WSPanicHandler wraps a WebSocket handler function to provide panic recovery
func WSPanicHandler(handler func(conn *websocket.Conn)) func(conn *websocket.Conn) {
    return func(conn *websocket.Conn) {
        defer func() {
            if r := recover(); r != nil {
                // Get the stack trace as a string
                stackTrace := string(debug.Stack())

                errMsg := fmt.Errorf("panic :: %s\n%s", r, stackTrace)

                LogPanic(errMsg)

                // Attempt to close the connection gracefully
                closeMsg := websocket.FormatCloseMessage(
                    websocket.CloseInternalServerErr,
                    "Internal server error occurred",
                )

                // Try to send close message to client
                err := conn.WriteControl(
                    websocket.CloseMessage,
                    closeMsg,
                    time.Now().Add(time.Second),
                )
                if err != nil {
                    LogException(fmt.Errorf("failed to send ws connection close message: %v", err), consts.Warning)
                }

                // Close the connection
                conn.Close()
            }
        }()

        handler(conn)
    }
}
```

#### Response Handling
The `WsResponse` function in the response package formats WebSocket responses in a consistent structure:
```go
func WsResponse(httpStatusCode int, errors interface{}, alerts interface{}, data interface{}) Response {
    response := Response{
        Status: Status{
            Code:   httpStatusCode,
            Errors: errors,
            Alerts: alerts,
        },
        Data: data,
    }
    return response
}
```

### WebSocket Client

A simple HTML test client is provided to test WebSocket functionality:
- Authentication section to obtain an AuthKey
- Connection section to establish WebSocket connection
- Response displays for both authentication and WebSocket connection

### Benefits

- **Real-time Updates**: Enables immediate delivery of information to connected clients
- **Reduced Overhead**: Eliminates the need for polling, reducing server load and network traffic
- **Bidirectional Communication**: Allows both client-to-server and server-to-client communication
- **Room-based Organization**: Groups connections by project and lot for targeted messaging
- **Security**: Integration with existing authentication system ensures secure connections
- **Error Resilience**: Robust error handling with panic recovery prevents server crashes
- **Clean Lifecycle Management**: Proper connection setup and teardown processes

## Conclusion

The ps-socket application follows a well-structured, modular architecture with clear separation of concerns. It uses the Beego framework for web server functionality, with custom packages for database access, logging, configuration, and other core features. The modular approach makes it easy to add new features or modify existing ones without affecting the entire application. The WebSocket implementation extends this architecture to provide real-time communication capabilities while maintaining the security and organization of the existing system.
