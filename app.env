# APP 
#===== 
APP_BUILD_REALSE=0.1

# Database
# ========
DB_DRIVER="postgres"
#DB_CON="user=postgres password=1234 dbname=procure_db host=************* sslmode=disable"
DB_CON="user=postgres password=1234 dbname=procure_db host=127.0.0.1 sslmode=disable"
DB_MAX_OPEN_CON=2
DB_MAX_IDLE_CON=1

# Database Read Replica
# =======================
DB_READ_CON="user=postgres password=1234 dbname=procure_db host=127.0.0.1 sslmode=disable"
DB_READ_MAX_OPEN_CON=2
DB_READ_MAX_IDLE_CON=1

# JWT Token
# ========== 
JWT_SECRET="procuresuit"

# AWS SecretManager
# ==================
AWS_SM_ACCESS_KEY="********************"
AWS_SM_SECRET_ACCESS_KEY="B9jtBHvJTvr88fZBZBfFxT2XkjwrgNLXGW/jJSpr"

# Local Storage
# ===============
STORAGE_PATH="../procuresuit_storage/temp"
APP_LOG_PATH="./logs/app-logs"
SYS_LOG_PATH="./logs/sys-logs"

# Encryption/Decryption Keys
# ===========================
AES_ENCRYPTION_KEY = "Ps4wvF3AAWmN4lbIxuaiG0uNjRO4iGDv" # Must 32 char length
AES_IV = "PEgQclKNasLzYMLf" # Must 16 char length

# SENTRY 
# ====== 
SENTRY_DSN=https://<EMAIL>/5
SENTRY_ENABLE_TRACING=true

# Password Encryption
# ====================
PW_AES_ENCRYPTION_KEY="pFw9SvqGNacaIRQMQAnozmnuoov9ubTT"
PW_AES_IV="P0mJGty7M64bKajK" 