<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            border: 1px solid #ddd;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        h2 {
            margin-top: 0;
        }
        .input-group {
            margin-bottom: 10px;
        }
        label {
            display: inline-block;
            width: 150px;
        }
        input {
            padding: 8px;
            width: 300px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .response {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            margin-top: 10px;
            min-height: 100px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>WebSocket Test Client</h1>
    
    <div class="section">
        <h2>Login</h2>
        <div class="input-group">
            <label for="loginApiKey">API Key:</label>
            <input type="text" id="loginApiKey" placeholder="Enter API Key" value="procuresuitweb"/>
        </div>
        <div class="input-group">
            <label for="loginOrigin">Origin:</label>
            <input type="text" id="loginOrigin" placeholder="Enter Origin" value="http://localhost:8088"/>
        </div>
        <div class="input-group">
            <label for="username">Username:</label>
            <input type="text" id="username" placeholder="Enter Username" value="superuser"/>
        </div>
        <div class="input-group">
            <label for="password">Password:</label>
            <input type="password" id="password" placeholder="Enter Password" value="UDBtSkd0eTdNNjRiS2FqSzrWDPxTdNngow=="/>
        </div>
        <div class="input-group">
            <label for="loginIp">IP:</label>
            <input type="text" id="loginIp" placeholder="Enter IP" value="***********"/>
        </div>
        <button id="loginBtn">Login</button>
        <div class="response" id="loginResponse"></div>
    </div>
    
    <div class="section">
        <h2>Authentication</h2>
        <div class="input-group">
            <label for="authorization">Auth Session:</label>
            <input type="text" id="authorization" placeholder="Enter Authorization Token" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJQcm94eUlkIjowLCJUb2tlblR5cGUiOiJhY2Nlc3MiLCJVc2VySWQiOjM5LCJSb2xlSWQiOjczLCJMZXZlbElkIjoyLCJQYXJlbnRSb2xlSWQiOjcsIkNvbXBhbnlJZCI6MywiQmlkZGluZ1VybCI6Imh0dHA6Ly9sb2NhbGhvc3Q6ODA4OCIsIlBhcnRpY2lwYW50SWQiOjAsIlByaXZpbGVnZXMiOiIjMTAxMDAxIzEwODAwMSMxMDUwMDEjMTA1MDA5IzEwNTAxMCMxMDUwMTIjMTA1MDA4IzEwNTAxMSMxMDUwMDUjMTA1MDA2IzEwNTAwMiMxMDUwMDMjMTA1MDA0IzEwNTAwNyMxMDcwMDEjMTA3MDA1IzEwNzAwNiMxMDcwMDcjMTA3MDA4IzEwNzAwMiMxMDcwMDMjMTA3MDA0IzEwMTEwMDEjMTAxMTAwNCMxMDExMDA1IzEwMTEwMDYjMTAxMTAwMiMxMDExMDAzIzEwMTAwMDEjMTA0MDAxIzEwNDAwMiMxMDQwMDMjMTA0MDA0IzEwNDAwNSMxMDQwMDYjMTA0MDA3IzEwNDAwOCMxMDQwMDkjMTA0MDEwIzEwNDAxMSMxMDQwMTIjMTA0MDEzIzEwMTIwMDgjMTAxMjAwMiMxMDEyMDAzIzEwMTIwMDUjMTAxMjAwNyMxMDEyMDAxIzEwMTIwMDYjMTAxMjAxMCMxMDEyMDA5IyIsIlN1YnNjcmlwdGlvbklkIjozNiwiZXhwIjoxNzQ4NDQxNzQyfQ.S9uljriNJRAc-i3YReMRxO8Zz1i9Tk4fNvU6G_-s3Og"/>
        </div>
        <div class="input-group">
            <label for="projectId">Project ID:</label>
            <input type="text" id="projectId" placeholder="Enter Project ID" value="ef7c6c6b-e4c6-48a5-9e7c-14e4eafc8caf"/>
        </div>
        <div class="input-group">
            <label for="lotId">Lot ID:</label>
            <input type="text" id="lotId" placeholder="Enter Lot ID" value="70040337-ed3b-4cb3-ab8f-3ab566e78990"/>
        </div>
        <div class="input-group">
            <label for="ip">IP:</label>
            <input type="text" id="ip" placeholder="Enter IP" value="***********"/>
        </div>
        <button id="authenticateBtn">Authenticate</button>
        <div class="response" id="authResponse"></div>
    </div>
    
    <div class="section">
        <h2>Init Connection</h2>
        <div class="input-group">
            <label for="authKey">Auth Key:</label>
            <input type="text" id="authKey" placeholder="Enter Auth Key from Authentication"/>
        </div>
        <button id="connectBtn">Connect</button>
        <div class="response" id="wsResponse"></div>
    </div>
    
    <div class="section">
        <h2>Send Message</h2>
        <div class="input-group">
            <label for="msgAuthKey">Auth Key:</label>
            <input type="text" id="msgAuthKey" placeholder="Enter Auth Key for this message"/>
        </div>
        <div class="input-group">
            <label for="jsonMessage">JSON Message:</label>
            <textarea id="jsonMessage" rows="5" style="width: 100%; padding: 8px; font-family: monospace;">{
  "action": "test_message",
  "data": {
    "id": 123,
    "name": "Test User"
  }
}</textarea>
        </div>
        <button id="sendMsgBtn" disabled>Send Message</button>
        <div class="response" id="msgResponse">Connect to WebSocket first</div>
    </div>

    <script>
        // Login functionality
        document.getElementById('loginBtn').addEventListener('click', function() {
            const apiKey = document.getElementById('loginApiKey').value;
            const origin = document.getElementById('loginOrigin').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const ip = document.getElementById('loginIp').value;
            
            const loginResponse = document.getElementById('loginResponse');
            loginResponse.textContent = 'Logging in...';
            
            fetch('http://localhost:8080/v1/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Api-Key': apiKey,
                    'Origin': origin
                },
                body: JSON.stringify({
                    username: username,
                    password: password,
                    ip: ip
                })
            })
            .then(response => response.json())
            .then(data => {
                loginResponse.textContent = JSON.stringify(data, null, 2);
                
                if (data.status && data.status.code === 200 && data.data && data.data.access_token) {
                    // Copy the access token to the Authentication section's Authorization field
                    document.getElementById('authorization').value = data.data.access_token;
                }
            })
            .catch(error => {
                loginResponse.textContent = 'Error: ' + error.message;
            });
        });
        
        // Authentication functionality
        document.getElementById('authenticateBtn').addEventListener('click', function() {
            // Use API Key and Origin from the Login section
            const apiKey = document.getElementById('loginApiKey').value;
            const origin = document.getElementById('loginOrigin').value;
            const authorization = document.getElementById('authorization').value;
            const projectId = document.getElementById('projectId').value;
            const lotId = document.getElementById('lotId').value;
            const ip = document.getElementById('ip').value;
            
            const authResponse = document.getElementById('authResponse');
            authResponse.textContent = 'Authenticating...';
            
            fetch('http://localhost:8088/v1/auth/do', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Api-Key': apiKey,
                    'Origin': origin,
                    'Authorization': authorization
                },
                body: JSON.stringify({
                    project_id: projectId,
                    lot_id: lotId,
                    ip: ip
                })
            })
            .then(response => response.json())
            .then(data => {
                authResponse.textContent = JSON.stringify(data, null, 2);
                
                if (data.status && data.status.code === 200 && data.data && data.data.auth_key) {
                    document.getElementById('authKey').value = data.data.auth_key;
                }
            })
            .catch(error => {
                authResponse.textContent = 'Error: ' + error.message;
            });
        });
        
        // Global WebSocket connection
        let activeSocket = null;
        
        document.getElementById('connectBtn').addEventListener('click', function() {
            const authKey = document.getElementById('authKey').value;
            const wsResponse = document.getElementById('wsResponse');
            const sendMsgBtn = document.getElementById('sendMsgBtn');
            const msgResponse = document.getElementById('msgResponse');
            
            // Close existing socket if any
            if (activeSocket) {
                activeSocket.close();
                activeSocket = null;
                sendMsgBtn.disabled = true;
                msgResponse.textContent = 'Connect to WebSocket first';
            }
            
            if (!authKey) {
                wsResponse.textContent = 'Error: Auth Key is required';
                return;
            }
            
            wsResponse.textContent = 'Connecting...';
            
            let protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            let host = window.location.hostname + (window.location.port ? ':' + window.location.port : '');
            
            const socket = new WebSocket(`${protocol}//${host}/v1/ws?auth_key=${authKey}`);
            
            socket.onopen = function(e) {
                wsResponse.textContent += '\nConnection established!';
                // Enable send message button
                sendMsgBtn.disabled = false;
                msgResponse.textContent = 'Connected. Ready to send messages.';
                activeSocket = socket;
                
                // Copy auth key to message auth key field for convenience
                document.getElementById('msgAuthKey').value = authKey;
            };
            
            socket.onmessage = function(event) {
                wsResponse.textContent += '\nReceived: ' + event.data;
                
                // If this is a response to our custom message, also show in the message response area
                try {
                    const data = JSON.parse(event.data);
                    if (data && !data.status) {  // Assuming status field is only in connection messages
                        msgResponse.textContent = 'Response received:\n' + event.data;
                    }
                } catch (e) {
                    // Not JSON or other issue, ignore
                }
            };
            
            socket.onclose = function(event) {
                if (event.wasClean) {
                    wsResponse.textContent += `\nConnection closed cleanly, code=${event.code}, reason=${event.reason}`;
                } else {
                    wsResponse.textContent += '\nConnection died';
                }
                // Disable send message button
                sendMsgBtn.disabled = true;
                msgResponse.textContent = 'WebSocket disconnected';
                activeSocket = null;
            };
            
            socket.onerror = function(error) {
                wsResponse.textContent += '\nError: ' + JSON.stringify(error);
                sendMsgBtn.disabled = true;
            };
        });
        
        // Event listener for the Send Message button
        document.getElementById('sendMsgBtn').addEventListener('click', function() {
            if (!activeSocket || activeSocket.readyState !== WebSocket.OPEN) {
                document.getElementById('msgResponse').textContent = 'Error: WebSocket not connected';
                return;
            }
            
            const jsonInput = document.getElementById('jsonMessage').value;
            const msgAuthKey = document.getElementById('msgAuthKey').value;
            const msgResponse = document.getElementById('msgResponse');
            
            // Validate JSON
            try {
                const jsonObject = JSON.parse(jsonInput);
                
                // Add auth_key to the message if provided
                if (msgAuthKey) {
                    jsonObject.auth_key = msgAuthKey;
                }
                
                // Convert back to string with the added auth_key
                const finalJsonMessage = JSON.stringify(jsonObject);
                
                // Send the message
                msgResponse.textContent = 'Sending message...';
                activeSocket.send(finalJsonMessage);
                
            } catch (error) {
                msgResponse.textContent = 'Error: Invalid JSON format. ' + error.message;
            }
        });
    </script>
</body>
</html>
